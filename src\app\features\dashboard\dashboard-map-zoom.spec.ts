import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ElementRef } from '@angular/core';
import { of } from 'rxjs';

import { DashboardComponent } from './dashboard.component';
import { SupabaseService } from '../../core/services/supabase.service';
import { AuthService } from '../../core/services/auth.service';
import { GeolocationService } from '../../core/services/geolocation.service';
import { MapNavigationService } from '../../core/services/map-navigation.service';

// Mock MapLibre GL
class MockMap {
  private _zoom = 4.0;
  private _center = [-119.4179, 36.7783];
  private _eventHandlers: { [key: string]: Function[] } = {};

  constructor(options: any) {
    console.log('🗺️ MockMap created with options:', options);
    this._zoom = options.zoom || 4.0;
    this._center = options.center || [-119.4179, 36.7783];
  }

  addControl(control: any, position: string) {
    console.log('🎛️ MockMap: Adding control at', position);
  }

  on(event: string, handler: Function) {
    console.log('📡 MockMap: Registering event handler for', event);
    if (!this._eventHandlers[event]) {
      this._eventHandlers[event] = [];
    }
    this._eventHandlers[event].push(handler);
    
    // Simulate immediate load event
    if (event === 'load') {
      setTimeout(() => handler(), 0);
    }
  }

  flyTo(options: any) {
    console.log('✈️ MockMap: flyTo called with:', options);
    if (options.zoom !== undefined) {
      this._zoom = options.zoom;
    }
    if (options.center !== undefined) {
      this._center = options.center;
    }
  }

  setZoom(zoom: number) {
    console.log('🔍 MockMap: setZoom called with:', zoom);
    this._zoom = zoom;
  }

  getZoom(): number {
    console.log('🔍 MockMap: getZoom returning:', this._zoom);
    return this._zoom;
  }

  getCenter(): { lng: number; lat: number } {
    return { lng: this._center[0], lat: this._center[1] };
  }

  fitBounds(bounds: any, options?: any) {
    console.log('📐 MockMap: fitBounds called with:', bounds, options);
    // Simulate zoom change that might occur with fitBounds
    this._zoom = options?.maxZoom || 10;
  }

  triggerEvent(event: string) {
    if (this._eventHandlers[event]) {
      this._eventHandlers[event].forEach(handler => handler());
    }
  }
}

class MockNavigationControl {}
class MockGeolocateControl {}
class MockMarker {
  constructor(options?: any) {}
  setLngLat(lngLat: [number, number]) { return this; }
  setPopup(popup: any) { return this; }
  addTo(map: any) { return this; }
  togglePopup() { return this; }
}
class MockPopup {
  constructor(options?: any) {}
  setHTML(html: string) { return this; }
}

// Mock the MapLibre GL module
(global as any).maplibregl = {
  Map: MockMap,
  NavigationControl: MockNavigationControl,
  GeolocateControl: MockGeolocateControl,
  Marker: MockMarker,
  Popup: MockPopup
};

describe('DashboardComponent - Map Zoom Investigation', () => {
  let component: DashboardComponent;
  let fixture: ComponentFixture<DashboardComponent>;
  let mockSupabaseService: jasmine.SpyObj<SupabaseService>;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockGeolocationService: jasmine.SpyObj<GeolocationService>;
  let mockMapNavigationService: jasmine.SpyObj<MapNavigationService>;

  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User'
  };

  const mockUserLocation = {
    latitude: 37.7749,
    longitude: -122.4194,
    accuracy: 10
  };

  beforeEach(async () => {
    const supabaseServiceSpy = jasmine.createSpyObj('SupabaseService', ['db']);
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['currentUser']);
    const geolocationServiceSpy = jasmine.createSpyObj('GeolocationService', ['getCurrentPosition']);
    const mapNavigationServiceSpy = jasmine.createSpyObj('MapNavigationService', ['navigationRequest$', 'clearNavigationRequest']);

    // Mock database responses
    const mockDbResponse = {
      from: jasmine.createSpy('from').and.returnValue({
        select: jasmine.createSpy('select').and.returnValue({
          gte: jasmine.createSpy('gte').and.returnValue({
            order: jasmine.createSpy('order').and.returnValue({
              limit: jasmine.createSpy('limit').and.returnValue(
                Promise.resolve({ data: [], error: null })
              )
            })
          }),
          count: jasmine.createSpy('count').and.returnValue(
            Promise.resolve({ data: null, error: null })
          )
        })
      })
    };

    await TestBed.configureTestingModule({
      imports: [DashboardComponent],
      providers: [
        { provide: SupabaseService, useValue: supabaseServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: GeolocationService, useValue: geolocationServiceSpy },
        { provide: MapNavigationService, useValue: mapNavigationServiceSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(DashboardComponent);
    component = fixture.componentInstance;
    mockSupabaseService = TestBed.inject(SupabaseService) as jasmine.SpyObj<SupabaseService>;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    mockGeolocationService = TestBed.inject(GeolocationService) as jasmine.SpyObj<GeolocationService>;
    mockMapNavigationService = TestBed.inject(MapNavigationService) as jasmine.SpyObj<MapNavigationService>;

    // Setup default mocks
    Object.defineProperty(mockAuthService, 'currentUser', {
      get: () => mockUser,
      configurable: true
    });
    
    Object.defineProperty(mockSupabaseService, 'db', {
      get: () => mockDbResponse,
      configurable: true
    });

    mockGeolocationService.getCurrentPosition.and.returnValue(of(mockUserLocation));
    mockMapNavigationService.navigationRequest$ = of(null);

    // Mock the map container element
    const mockMapContainer = {
      nativeElement: document.createElement('div')
    };
    component.mapContainer = mockMapContainer as ElementRef<HTMLDivElement>;
  });

  describe('Map Initialization Zoom Level', () => {
    it('should initialize map with zoom level 4.0', async () => {
      console.log('🧪 Testing initial map zoom level...');
      
      // Trigger component initialization
      fixture.detectChanges();
      await fixture.whenStable();
      
      // Access the private map property for testing
      const map = (component as any).map as MockMap;
      
      expect(map).toBeDefined();
      expect(map.getZoom()).toBe(4.0);
      
      console.log('✅ Map initialized with correct zoom level:', map.getZoom());
    });

    it('should maintain zoom level 4.0 after user location is obtained', async () => {
      console.log('🧪 Testing zoom level after user location obtained...');
      
      fixture.detectChanges();
      await fixture.whenStable();
      
      const map = (component as any).map as MockMap;
      const initialZoom = map.getZoom();
      
      // Simulate user location being obtained
      mockGeolocationService.getCurrentPosition.and.returnValue(of(mockUserLocation));
      
      // Trigger getUserLocation manually
      (component as any).getUserLocation();
      await fixture.whenStable();
      
      const finalZoom = map.getZoom();
      
      expect(initialZoom).toBe(4.0);
      expect(finalZoom).toBe(4.0);
      
      console.log('✅ Zoom level maintained after user location:', { initial: initialZoom, final: finalZoom });
    });

    it('should not automatically center on user location', async () => {
      console.log('🧪 Testing that map does not auto-center on user location...');
      
      fixture.detectChanges();
      await fixture.whenStable();
      
      const map = (component as any).map as MockMap;
      const initialCenter = map.getCenter();
      
      // Simulate user location being obtained
      (component as any).getUserLocation();
      await fixture.whenStable();
      
      const finalCenter = map.getCenter();
      
      // Center should remain at California coordinates, not user location
      expect(finalCenter.lng).toBeCloseTo(-119.4179, 4);
      expect(finalCenter.lat).toBeCloseTo(36.7783, 4);
      
      console.log('✅ Map center maintained at California coordinates:', finalCenter);
    });

    it('should not call fitBounds or other zoom-changing methods during initialization', async () => {
      console.log('🧪 Testing that no zoom-changing methods are called during init...');
      
      const map = new MockMap({ zoom: 4.0, center: [-119.4179, 36.7783] });
      spyOn(map, 'fitBounds');
      spyOn(map, 'setZoom');
      spyOn(map, 'flyTo');
      
      // Mock the map creation to use our spy
      spyOn(component as any, 'initializeMap').and.callFake(() => {
        (component as any).map = map;
        map.triggerEvent('load');
      });
      
      fixture.detectChanges();
      await fixture.whenStable();
      
      expect(map.fitBounds).not.toHaveBeenCalled();
      expect(map.setZoom).not.toHaveBeenCalled();
      expect(map.flyTo).not.toHaveBeenCalled();
      
      console.log('✅ No zoom-changing methods called during initialization');
    });
  });

  describe('Map Event Handlers', () => {
    it('should handle map load event without changing zoom', async () => {
      console.log('🧪 Testing map load event handling...');
      
      fixture.detectChanges();
      await fixture.whenStable();
      
      const map = (component as any).map as MockMap;
      const zoomBeforeLoad = map.getZoom();
      
      // Trigger load event
      map.triggerEvent('load');
      await fixture.whenStable();
      
      const zoomAfterLoad = map.getZoom();
      
      expect(zoomBeforeLoad).toBe(4.0);
      expect(zoomAfterLoad).toBe(4.0);
      
      console.log('✅ Zoom level unchanged after load event:', { before: zoomBeforeLoad, after: zoomAfterLoad });
    });
  });
});
