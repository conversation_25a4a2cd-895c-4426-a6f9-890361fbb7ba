import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';

import { HostPlantFormComponent } from './host-plant-form.component';
import { SupabaseService } from '../../../core/services/supabase.service';
import { AuthService } from '../../../core/services/auth.service';
import { MapNavigationService } from '../../../core/services/map-navigation.service';
import { ObservationType, GeoLocation } from '../../../core/models/observation.model';

describe('HostPlantFormComponent - Critical Issue Investigation', () => {
  let component: HostPlantFormComponent;
  let fixture: ComponentFixture<HostPlantFormComponent>;
  let mockSupabaseService: jasmine.SpyObj<SupabaseService>;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockMapNavigationService: jasmine.SpyObj<MapNavigationService>;
  let mockRouter: jasmine.SpyObj<Router>;

  const mockUser = {
    id: 'test-user-id',
    email: '<EMAIL>',
    firstName: 'Test',
    lastName: 'User'
  };

  const mockLocation: GeoLocation = {
    latitude: 36.7783,
    longitude: -119.4179,
    accuracy: 10,
    address: 'Central Valley, CA'
  };

  const mockDbResponse = {
    from: jasmine.createSpy('from').and.returnValue({
      insert: jasmine.createSpy('insert').and.returnValue({
        select: jasmine.createSpy('select').and.returnValue({
          single: jasmine.createSpy('single').and.returnValue(
            Promise.resolve({ data: { id: 'test-observation-id' }, error: null })
          )
        })
      })
    })
  };

  beforeEach(async () => {
    const supabaseServiceSpy = jasmine.createSpyObj('SupabaseService', ['db']);
    const authServiceSpy = jasmine.createSpyObj('AuthService', ['currentUser']);
    const mapNavigationServiceSpy = jasmine.createSpyObj('MapNavigationService', ['navigateToNewObservation']);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    await TestBed.configureTestingModule({
      imports: [HostPlantFormComponent, ReactiveFormsModule],
      providers: [
        { provide: SupabaseService, useValue: supabaseServiceSpy },
        { provide: AuthService, useValue: authServiceSpy },
        { provide: MapNavigationService, useValue: mapNavigationServiceSpy },
        { provide: Router, useValue: routerSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(HostPlantFormComponent);
    component = fixture.componentInstance;
    mockSupabaseService = TestBed.inject(SupabaseService) as jasmine.SpyObj<SupabaseService>;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    mockMapNavigationService = TestBed.inject(MapNavigationService) as jasmine.SpyObj<MapNavigationService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;

    // Setup default mocks
    Object.defineProperty(mockAuthService, 'currentUser', {
      get: () => mockUser,
      configurable: true
    });
    
    Object.defineProperty(mockSupabaseService, 'db', {
      get: () => mockDbResponse,
      configurable: true
    });

    fixture.detectChanges();
  });

  describe('Form Submission Investigation', () => {
    beforeEach(() => {
      // Setup valid form data
      component.hostPlantForm.patchValue({
        species: 'russian_thistle',
        density: 'medium',
        healthStatus: 'healthy',
        growthStage: 'vegetative',
        estimatedCount: '50',
        coverageArea: '100.5',
        notes: 'Test observation'
      });
      component.selectedLocation = mockLocation;
    });

    it('should successfully submit with valid data', async () => {
      console.log('🧪 Testing successful form submission...');
      
      await component.onSubmit();
      
      expect(component.submitSuccess).toBe(true);
      expect(component.submitError).toBe('');
      expect(component.isSubmitting).toBe(false);
      
      console.log('✅ Form submission successful');
    });

    it('should handle authentication errors', async () => {
      console.log('🧪 Testing authentication error handling...');
      
      Object.defineProperty(mockAuthService, 'currentUser', {
        get: () => null,
        configurable: true
      });
      
      await component.onSubmit();
      
      expect(component.submitError).toBe('Failed to save observation. Please try again.');
      expect(component.submitSuccess).toBe(false);
      
      console.log('✅ Authentication error handled correctly');
    });

    it('should handle database insertion errors', async () => {
      console.log('🧪 Testing database error handling...');
      
      const errorDbResponse = {
        from: jasmine.createSpy('from').and.returnValue({
          insert: jasmine.createSpy('insert').and.returnValue({
            select: jasmine.createSpy('select').and.returnValue({
              single: jasmine.createSpy('single').and.returnValue(
                Promise.resolve({ data: null, error: { message: 'Database error' } })
              )
            })
          })
        })
      };
      
      Object.defineProperty(mockSupabaseService, 'db', {
        get: () => errorDbResponse,
        configurable: true
      });
      
      await component.onSubmit();
      
      expect(component.submitError).toBe('Failed to save observation. Please try again.');
      expect(component.submitSuccess).toBe(false);
      
      console.log('✅ Database error handled correctly');
    });

    it('should create correct observation object structure', async () => {
      console.log('🧪 Testing observation object structure...');
      
      const insertSpy = mockDbResponse.from().insert();
      
      await component.onSubmit();
      
      expect(insertSpy).toHaveBeenCalledWith(jasmine.objectContaining({
        user_id: mockUser.id,
        user_name: 'Test User',
        user_email: mockUser.email,
        type: ObservationType.HOST_PLANT,
        latitude: mockLocation.latitude,
        longitude: mockLocation.longitude,
        accuracy: mockLocation.accuracy,
        address: mockLocation.address,
        host_plant_data: jasmine.objectContaining({
          species: 'russian_thistle',
          density: 'medium',
          healthStatus: 'healthy',
          growthStage: 'vegetative',
          estimatedCount: 50,
          coverageArea: 100.5
        }),
        photos: [],
        notes: 'Test observation'
      }));
      
      console.log('✅ Observation object structure is correct');
    });

    it('should validate required location', async () => {
      console.log('🧪 Testing location validation...');
      
      component.selectedLocation = null;
      
      await component.onSubmit();
      
      // Should not attempt submission without location
      expect(mockDbResponse.from).not.toHaveBeenCalled();
      
      console.log('✅ Location validation working correctly');
    });

    it('should call MapNavigationService after successful submission', async () => {
      console.log('🧪 Testing MapNavigationService integration...');
      
      await component.onSubmit();
      
      // Wait for setTimeout to complete
      await new Promise(resolve => setTimeout(resolve, 2100));
      
      expect(mockMapNavigationService.navigateToNewObservation).toHaveBeenCalledWith(
        mockLocation.latitude,
        mockLocation.longitude,
        'test-observation-id',
        'host_plant'
      );
      
      console.log('✅ MapNavigationService called correctly');
    });
  });

  describe('Form Validation', () => {
    it('should require all mandatory fields', () => {
      console.log('🧪 Testing form validation...');
      
      expect(component.hostPlantForm.valid).toBe(false);
      
      component.hostPlantForm.patchValue({
        species: 'russian_thistle',
        density: 'medium',
        healthStatus: 'healthy',
        growthStage: 'vegetative'
      });
      
      expect(component.hostPlantForm.valid).toBe(true);
      
      console.log('✅ Form validation working correctly');
    });
  });
});
