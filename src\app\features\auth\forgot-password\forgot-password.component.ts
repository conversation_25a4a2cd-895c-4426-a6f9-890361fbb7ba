import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { AuthService } from '../../../core/services/auth.service';
import { LoadingSpinnerComponent } from '../../../shared/components/loading-spinner/loading-spinner.component';

@Component({
  selector: 'app-forgot-password',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterModule, LoadingSpinnerComponent],
  template: `
    <div class="auth-container">
      <div class="auth-card">
        <div class="auth-header">
          <h1>BCTV Management System</h1>
          <h2>Reset Password</h2>
          <p>Enter your email address and we'll send you a link to reset your password</p>
        </div>

        <form [formGroup]="forgotPasswordForm" (ngSubmit)="onSubmit()" class="auth-form" *ngIf="!emailSent">
          <div class="form-group">
            <label for="email">Email Address</label>
            <input
              id="email"
              type="email"
              formControlName="email"
              class="form-control"
              [class.error]="isFieldInvalid('email')"
              placeholder="Enter your email address">
            <div class="error-message" *ngIf="isFieldInvalid('email')">
              <span *ngIf="forgotPasswordForm.get('email')?.errors?.['required']">Email is required</span>
              <span *ngIf="forgotPasswordForm.get('email')?.errors?.['email']">Please enter a valid email</span>
            </div>
          </div>

          <div class="form-actions">
            <button
              type="submit"
              class="btn btn-primary"
              [disabled]="forgotPasswordForm.invalid || isLoading">
              <span *ngIf="!isLoading">Send Reset Link</span>
              <app-loading-spinner *ngIf="isLoading" size="small"></app-loading-spinner>
            </button>
          </div>

          <div class="error-message" *ngIf="errorMessage">
            {{errorMessage}}
          </div>
        </form>

        <div class="success-state" *ngIf="emailSent">
          <div class="success-icon">✅</div>
          <h3>Check Your Email</h3>
          <p>We've sent a password reset link to <strong>{{submittedEmail}}</strong></p>
          <p class="help-text">
            If you don't see the email in your inbox, please check your spam folder. 
            The link will expire in 1 hour for security reasons.
          </p>
          <button (click)="resendEmail()" class="btn btn-secondary" [disabled]="isLoading">
            <span *ngIf="!isLoading">Resend Email</span>
            <app-loading-spinner *ngIf="isLoading" size="small"></app-loading-spinner>
          </button>
        </div>

        <div class="auth-footer">
          <p>
            Remember your password? 
            <a routerLink="/auth/login" class="link">Back to Sign In</a>
          </p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .auth-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 1rem;
    }

    .auth-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      padding: 2rem;
      width: 100%;
      max-width: 400px;
    }

    .auth-header {
      text-align: center;
      margin-bottom: 2rem;
    }

    .auth-header h1 {
      color: #333;
      font-size: 1.5rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
    }

    .auth-header h2 {
      color: #007bff;
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0 0 0.5rem 0;
    }

    .auth-header p {
      color: #666;
      font-size: 0.875rem;
      margin: 0;
      line-height: 1.4;
    }

    .auth-form {
      margin-bottom: 1.5rem;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    .form-group label {
      display: block;
      font-weight: 500;
      color: #333;
      margin-bottom: 0.5rem;
      font-size: 0.875rem;
    }

    .form-control {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 0.875rem;
      transition: border-color 0.2s, box-shadow 0.2s;
      box-sizing: border-box;
    }

    .form-control:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }

    .form-control.error {
      border-color: #dc3545;
    }

    .form-control.error:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    }

    .error-message {
      color: #dc3545;
      font-size: 0.75rem;
      margin-top: 0.25rem;
    }

    .form-actions {
      margin-top: 2rem;
    }

    .btn {
      width: 100%;
      padding: 0.75rem;
      border: none;
      border-radius: 6px;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      margin-bottom: 1rem;
    }

    .btn-primary {
      background: #007bff;
      color: white;
    }

    .btn-primary:hover:not(:disabled) {
      background: #0056b3;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover:not(:disabled) {
      background: #545b62;
    }

    .btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }

    .success-state {
      text-align: center;
      padding: 2rem 0;
    }

    .success-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    .success-state h3 {
      color: #333;
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0 0 1rem 0;
    }

    .success-state p {
      color: #666;
      font-size: 0.875rem;
      margin: 0 0 1rem 0;
      line-height: 1.4;
    }

    .help-text {
      font-size: 0.75rem !important;
      color: #999 !important;
    }

    .auth-footer {
      text-align: center;
      border-top: 1px solid #eee;
      padding-top: 1.5rem;
    }

    .auth-footer p {
      margin: 0.5rem 0;
      font-size: 0.875rem;
      color: #666;
    }

    .link {
      color: #007bff;
      text-decoration: none;
      font-weight: 500;
    }

    .link:hover {
      text-decoration: underline;
    }

    @media (max-width: 480px) {
      .auth-container {
        padding: 0.5rem;
      }
      
      .auth-card {
        padding: 1.5rem;
      }
    }
  `]
})
export class ForgotPasswordComponent implements OnInit {
  forgotPasswordForm: FormGroup;
  isLoading = false;
  errorMessage = '';
  emailSent = false;
  submittedEmail = '';

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router
  ) {
    this.forgotPasswordForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]]
    });
  }

  ngOnInit() {
    // Redirect if already authenticated
    if (this.authService.isAuthenticated) {
      this.router.navigate(['/dashboard']);
    }
  }

  onSubmit() {
    if (this.forgotPasswordForm.valid) {
      this.sendResetEmail();
    } else {
      this.markFormGroupTouched();
    }
  }

  private sendResetEmail() {
    this.isLoading = true;
    this.errorMessage = '';

    const email = this.forgotPasswordForm.get('email')?.value;

    this.authService.resetPassword(email).subscribe({
      next: () => {
        this.isLoading = false;
        this.emailSent = true;
        this.submittedEmail = email;
      },
      error: (error) => {
        this.isLoading = false;
        this.errorMessage = this.getErrorMessage(error);
      }
    });
  }

  resendEmail() {
    this.sendResetEmail();
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.forgotPasswordForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  private markFormGroupTouched() {
    Object.keys(this.forgotPasswordForm.controls).forEach(key => {
      const control = this.forgotPasswordForm.get(key);
      control?.markAsTouched();
    });
  }

  private getErrorMessage(error: any): string {
    if (error?.message) {
      if (error.message.includes('User not found')) {
        return 'No account found with this email address.';
      }
      if (error.message.includes('rate limit')) {
        return 'Too many requests. Please wait a few minutes before trying again.';
      }
      return error.message;
    }
    return 'An error occurred while sending the reset email. Please try again.';
  }
}
