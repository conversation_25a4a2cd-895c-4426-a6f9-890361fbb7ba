import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject } from 'rxjs';

export interface MapNavigationRequest {
  latitude: number;
  longitude: number;
  zoom?: number;
  observationId?: string;
  observationType?: string;
  showPopup?: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class MapNavigationService {
  private navigationRequestSubject = new BehaviorSubject<MapNavigationRequest | null>(null);
  public navigationRequest$ = this.navigationRequestSubject.asObservable();

  constructor(private router: Router) {}

  /**
   * Navigate to dashboard and focus on a specific location
   */
  navigateToLocation(request: MapNavigationRequest): void {
    console.log('🗺️ MapNavigationService: Navigating to location:', request);
    
    // Set the navigation request
    this.navigationRequestSubject.next(request);
    
    // Navigate to dashboard
    this.router.navigate(['/dashboard']);
  }

  /**
   * Navigate to a newly created observation
   */
  navigateToNewObservation(
    latitude: number, 
    longitude: number, 
    observationId: string, 
    observationType: string
  ): void {
    const request: MapNavigationRequest = {
      latitude,
      longitude,
      zoom: 13, // Detailed view for new observations
      observationId,
      observationType,
      showPopup: true
    };

    console.log('🎯 MapNavigationService: Navigating to new observation:', request);
    this.navigateToLocation(request);
  }

  /**
   * Clear the current navigation request
   */
  clearNavigationRequest(): void {
    this.navigationRequestSubject.next(null);
  }

  /**
   * Get the current navigation request without subscribing
   */
  getCurrentNavigationRequest(): MapNavigationRequest | null {
    return this.navigationRequestSubject.value;
  }
}
