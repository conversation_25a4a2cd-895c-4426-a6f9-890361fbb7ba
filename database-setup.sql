-- BCTV Management System Database Setup
-- Run this SQL in your Supabase SQL Editor to create the required tables

-- First, check if observations table exists and add missing columns
DO $$
BEGIN
    -- Create table if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'observations') THEN
        CREATE TABLE observations (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            type VARCHAR(50) NOT NULL CHECK (type IN ('host_plant', 'blh_observation', 'bctv_symptoms', 'eradication_effort')),
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
            latitude DECIMAL(10, 8) NOT NULL,
            longitude DECIMAL(11, 8) NOT NULL,
            data JSONB,
            notes TEXT,
            photos TEXT[], -- Array of photo URLs
            status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'archived', 'deleted'))
        );
    ELSE
        -- Table exists, add missing columns if they don't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'observations' AND column_name = 'data') THEN
            ALTER TABLE observations ADD COLUMN data JSONB;
        END IF;

        IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'observations' AND column_name = 'notes') THEN
            ALTER TABLE observations ADD COLUMN notes TEXT;
        END IF;

        IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'observations' AND column_name = 'photos') THEN
            ALTER TABLE observations ADD COLUMN photos TEXT[];
        END IF;

        IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'observations' AND column_name = 'status') THEN
            ALTER TABLE observations ADD COLUMN status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'archived', 'deleted'));
        END IF;

        IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_name = 'observations' AND column_name = 'updated_at') THEN
            ALTER TABLE observations ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
        END IF;
    END IF;
END
$$;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_observations_type ON observations(type);
CREATE INDEX IF NOT EXISTS idx_observations_created_at ON observations(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_observations_user_id ON observations(user_id);
CREATE INDEX IF NOT EXISTS idx_observations_location ON observations(latitude, longitude);

-- Enable Row Level Security (RLS)
ALTER TABLE observations ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (drop existing ones first to avoid conflicts)
DROP POLICY IF EXISTS "Users can view all observations" ON observations;
DROP POLICY IF EXISTS "Users can insert their own observations" ON observations;
DROP POLICY IF EXISTS "Users can update their own observations" ON observations;
DROP POLICY IF EXISTS "Users can delete their own observations" ON observations;

CREATE POLICY "Users can view all observations" ON observations
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own observations" ON observations
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own observations" ON observations
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own observations" ON observations
    FOR DELETE USING (auth.uid() = user_id);

-- Create sample data for testing
INSERT INTO observations (type, latitude, longitude, data, notes, user_id) VALUES
    ('host_plant', 36.7783, -119.4179, '{"species": "Sugar beet", "density": "high", "healthStatus": "healthy"}', 'Sample host plant observation', NULL),
    ('blh_observation', 36.7500, -119.4000, '{"adultCount": 15, "nymphCount": 8, "behavior": "feeding"}', 'Sample BLH observation', NULL),
    ('bctv_symptoms', 36.7200, -119.3800, '{"severity": "moderate", "affectedPlantCount": 5, "symptoms": ["yellowing", "stunting"]}', 'Sample BCTV symptoms', NULL),
    ('eradication_effort', 36.7100, -119.3600, '{"method": "insecticide", "areaSize": 100, "effectiveness": "high"}', 'Sample eradication effort', NULL)
ON CONFLICT DO NOTHING;

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_observations_updated_at BEFORE UPDATE ON observations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant permissions
GRANT ALL ON observations TO authenticated;
GRANT SELECT ON observations TO anon;
