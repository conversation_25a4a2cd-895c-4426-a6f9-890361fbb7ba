import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute, RouterModule } from '@angular/router';
import { AuthService } from '../../../core/services/auth.service';
import { LoadingSpinnerComponent } from '../../../shared/components/loading-spinner/loading-spinner.component';

@Component({
  selector: 'app-reset-password',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterModule, LoadingSpinnerComponent],
  template: `
    <div class="auth-container">
      <div class="auth-card">
        <div class="auth-header">
          <h1>BCTV Management System</h1>
          <h2>Set New Password</h2>
          <p>Enter your new password below</p>
        </div>

        <form [formGroup]="resetPasswordForm" (ngSubmit)="onSubmit()" class="auth-form" *ngIf="!passwordReset && !tokenExpired">
          <div class="form-group">
            <label for="password">New Password</label>
            <input
              id="password"
              type="password"
              formControlName="password"
              class="form-control"
              [class.error]="isFieldInvalid('password')"
              placeholder="Enter new password">
            <div class="error-message" *ngIf="isFieldInvalid('password')">
              <span *ngIf="resetPasswordForm.get('password')?.errors?.['required']">Password is required</span>
              <span *ngIf="resetPasswordForm.get('password')?.errors?.['minlength']">Password must be at least 6 characters</span>
            </div>
          </div>

          <div class="form-group">
            <label for="confirmPassword">Confirm New Password</label>
            <input
              id="confirmPassword"
              type="password"
              formControlName="confirmPassword"
              class="form-control"
              [class.error]="isFieldInvalid('confirmPassword')"
              placeholder="Confirm new password">
            <div class="error-message" *ngIf="isFieldInvalid('confirmPassword')">
              <span *ngIf="resetPasswordForm.get('confirmPassword')?.errors?.['required']">Please confirm your password</span>
              <span *ngIf="resetPasswordForm.get('confirmPassword')?.errors?.['passwordMismatch']">Passwords do not match</span>
            </div>
          </div>

          <div class="form-actions">
            <button
              type="submit"
              class="btn btn-primary"
              [disabled]="resetPasswordForm.invalid || isLoading">
              <span *ngIf="!isLoading">Update Password</span>
              <app-loading-spinner *ngIf="isLoading" size="small"></app-loading-spinner>
            </button>
          </div>

          <div class="error-message" *ngIf="errorMessage">
            {{errorMessage}}
          </div>
        </form>

        <div class="success-state" *ngIf="passwordReset">
          <div class="success-icon">✅</div>
          <h3>Password Updated Successfully</h3>
          <p>Your password has been updated. You can now sign in with your new password.</p>
          <button (click)="goToLogin()" class="btn btn-primary">
            Go to Sign In
          </button>
        </div>

        <div class="error-state" *ngIf="tokenExpired">
          <div class="error-icon">❌</div>
          <h3>Reset Link Expired</h3>
          <p>This password reset link has expired or is invalid. Please request a new one.</p>
          <button (click)="goToForgotPassword()" class="btn btn-primary">
            Request New Reset Link
          </button>
        </div>

        <div class="auth-footer">
          <p>
            Remember your password?
            <a routerLink="/auth/login" class="link">Back to Sign In</a>
          </p>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .auth-container {
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      padding: 1rem;
    }

    .auth-card {
      background: white;
      border-radius: 12px;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      padding: 2rem;
      width: 100%;
      max-width: 400px;
    }

    .auth-header {
      text-align: center;
      margin-bottom: 2rem;
    }

    .auth-header h1 {
      color: #333;
      font-size: 1.5rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
    }

    .auth-header h2 {
      color: #007bff;
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0 0 0.5rem 0;
    }

    .auth-header p {
      color: #666;
      font-size: 0.875rem;
      margin: 0;
    }

    .auth-form {
      margin-bottom: 1.5rem;
    }

    .form-group {
      margin-bottom: 1.5rem;
    }

    .form-group label {
      display: block;
      font-weight: 500;
      color: #333;
      margin-bottom: 0.5rem;
      font-size: 0.875rem;
    }

    .form-control {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 0.875rem;
      transition: border-color 0.2s, box-shadow 0.2s;
      box-sizing: border-box;
    }

    .form-control:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }

    .form-control.error {
      border-color: #dc3545;
    }

    .form-control.error:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    }

    .error-message {
      color: #dc3545;
      font-size: 0.75rem;
      margin-top: 0.25rem;
    }

    .form-actions {
      margin-top: 2rem;
    }

    .btn {
      width: 100%;
      padding: 0.75rem;
      border: none;
      border-radius: 6px;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
    }

    .btn-primary {
      background: #007bff;
      color: white;
    }

    .btn-primary:hover:not(:disabled) {
      background: #0056b3;
    }

    .btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }

    .success-state, .error-state {
      text-align: center;
      padding: 2rem 0;
    }

    .success-icon, .error-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    .success-state h3, .error-state h3 {
      color: #333;
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0 0 1rem 0;
    }

    .success-state p, .error-state p {
      color: #666;
      font-size: 0.875rem;
      margin: 0 0 2rem 0;
      line-height: 1.4;
    }

    .auth-footer {
      text-align: center;
      border-top: 1px solid #eee;
      padding-top: 1.5rem;
    }

    .auth-footer p {
      margin: 0.5rem 0;
      font-size: 0.875rem;
      color: #666;
    }

    .link {
      color: #007bff;
      text-decoration: none;
      font-weight: 500;
    }

    .link:hover {
      text-decoration: underline;
    }

    @media (max-width: 480px) {
      .auth-container {
        padding: 0.5rem;
      }

      .auth-card {
        padding: 1.5rem;
      }
    }
  `]
})
export class ResetPasswordComponent implements OnInit {
  resetPasswordForm: FormGroup;
  isLoading = false;
  errorMessage = '';
  passwordReset = false;
  tokenExpired = false;
  private accessToken = '';
  private refreshToken = '';

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute
  ) {
    this.resetPasswordForm = this.fb.group({
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', [Validators.required]]
    }, { validators: this.passwordMatchValidator });
  }

  ngOnInit() {
    console.log('🔐 Reset password component initialized');

    // Extract tokens from URL fragments (for 303 redirects)
    this.route.fragment.subscribe(fragment => {
      console.log('📋 URL fragment:', fragment);
      if (fragment) {
        const params = new URLSearchParams(fragment);
        this.accessToken = params.get('access_token') || '';
        this.refreshToken = params.get('refresh_token') || '';

        console.log('🔑 Extracted tokens:', {
          hasAccessToken: !!this.accessToken,
          hasRefreshToken: !!this.refreshToken
        });

        if (this.accessToken) {
          this.setSessionWithTokens();
        } else {
          this.tokenExpired = true;
        }
      } else {
        // Also check query parameters as fallback
        this.route.queryParams.subscribe(params => {
          console.log('📋 Query params:', params);
          this.accessToken = params['access_token'] || '';
          this.refreshToken = params['refresh_token'] || '';

          if (this.accessToken) {
            console.log('🔑 Found tokens in query params');
            this.setSessionWithTokens();
          } else {
            console.log('❌ No tokens found, marking as expired');
            this.tokenExpired = true;
          }
        });
      }
    });
  }

  private async setSessionWithTokens() {
    try {
      console.log('🔄 Setting session with tokens...');
      const { data, error } = await this.authService.supabaseService.auth.setSession({
        access_token: this.accessToken,
        refresh_token: this.refreshToken
      });

      if (error) {
        console.error('❌ Error setting session:', error);
        this.tokenExpired = true;
      } else {
        console.log('✅ Session set successfully');
      }
    } catch (error) {
      console.error('❌ Exception setting session:', error);
      this.tokenExpired = true;
    }
  }

  onSubmit() {
    if (this.resetPasswordForm.valid && this.accessToken) {
      this.updatePassword();
    } else {
      this.markFormGroupTouched();
    }
  }

  private updatePassword() {
    this.isLoading = true;
    this.errorMessage = '';

    const newPassword = this.resetPasswordForm.get('password')?.value;

    console.log('🔄 Updating password...');

    this.authService.updatePassword(newPassword).subscribe({
      next: () => {
        console.log('✅ Password updated successfully');
        this.isLoading = false;
        this.passwordReset = true;
      },
      error: (error) => {
        console.error('❌ Password update failed:', error);
        this.isLoading = false;
        this.errorMessage = this.getErrorMessage(error);
      }
    });
  }

  goToLogin() {
    this.router.navigate(['/auth/login']);
  }

  goToForgotPassword() {
    this.router.navigate(['/auth/forgot-password']);
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.resetPasswordForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  private markFormGroupTouched() {
    Object.keys(this.resetPasswordForm.controls).forEach(key => {
      const control = this.resetPasswordForm.get(key);
      control?.markAsTouched();
    });
  }

  private passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');

    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
    } else if (confirmPassword?.errors?.['passwordMismatch']) {
      delete confirmPassword.errors['passwordMismatch'];
      if (Object.keys(confirmPassword.errors).length === 0) {
        confirmPassword.setErrors(null);
      }
    }

    return null;
  }

  private getErrorMessage(error: any): string {
    if (error?.message) {
      if (error.message.includes('Invalid token')) {
        return 'The reset link is invalid or has expired. Please request a new one.';
      }
      if (error.message.includes('weak password')) {
        return 'Password is too weak. Please choose a stronger password.';
      }
      return error.message;
    }
    return 'An error occurred while updating your password. Please try again.';
  }
}
