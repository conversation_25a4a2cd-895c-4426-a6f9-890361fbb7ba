import { Component, OnInit, AfterViewInit, ElementRef, ViewChild, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { Map, NavigationControl, Marker, Popup } from 'maplibre-gl';
import { PredictionService } from '../../../core/services/prediction.service';
import { GeolocationService } from '../../../core/services/geolocation.service';
import { SupabaseService } from '../../../core/services/supabase.service';
import { LoadingSpinnerComponent } from '../../../shared/components/loading-spinner/loading-spinner.component';
import { BCTVPrediction, RiskLevel, PredictionRequest } from '../../../core/models/prediction.model';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-prediction-view',
  standalone: true,
  imports: [CommonModule, LoadingSpinnerComponent],
  template: `
    <div class="prediction-container">
      <div class="prediction-header">
        <h1>BCTV Risk Predictions</h1>
        <p>AI-powered risk assessment for BCTV outbreaks</p>
        <button (click)="goBack()" class="back-btn">← Back to Dashboard</button>
      </div>

      <div class="prediction-controls">
        <button
          (click)="generatePrediction()"
          class="btn btn-primary"
          [disabled]="isGenerating">
          <span *ngIf="!isGenerating">🔮 Generate Prediction</span>
          <app-loading-spinner *ngIf="isGenerating" size="small"></app-loading-spinner>
        </button>

        <button
          (click)="getCurrentLocation()"
          class="btn btn-secondary"
          [disabled]="isGettingLocation">
          <span *ngIf="!isGettingLocation">📍 Use Current Location</span>
          <app-loading-spinner *ngIf="isGettingLocation" size="small"></app-loading-spinner>
        </button>

        <button
          (click)="generatePredictionForSelectedLocation()"
          class="btn btn-secondary"
          [disabled]="!selectedLocation || isGenerating">
          <span>🗺️ Generate for Selected Location</span>
        </button>
      </div>

      <!-- Location Selection Map -->
      <div class="location-selection" *ngIf="!currentPrediction">
        <h3>📍 Select Location for Prediction</h3>
        <p>Click on the map to select a location, or use your current location.</p>
        <div #mapContainer class="prediction-map"></div>
        <div class="selected-location" *ngIf="selectedLocation">
          <p><strong>Selected Location:</strong> {{selectedLocation.latitude.toFixed(4)}}, {{selectedLocation.longitude.toFixed(4)}}</p>
        </div>
      </div>

      <div class="prediction-content" *ngIf="currentPrediction; else noPrediction">
        <div class="risk-overview">
          <div class="risk-level-card" [class]="currentPrediction.riskLevel">
            <div class="risk-level-header">
              <h2>Risk Level</h2>
              <div class="risk-score">{{currentPrediction.riskScore}}/100</div>
            </div>
            <div class="risk-level-badge">
              {{getRiskLevelDisplay(currentPrediction.riskLevel)}}
            </div>
            <div class="confidence">
              Confidence: {{(currentPrediction.confidence * 100).toFixed(0)}}%
            </div>
          </div>

          <div class="location-info">
            <h3>📍 Location</h3>
            <p>{{currentPrediction.location.address ||
                (currentPrediction.location.latitude.toFixed(4) + ', ' +
                 currentPrediction.location.longitude.toFixed(4))}}</p>
            <p class="validity">
              Valid: {{currentPrediction.validFrom | date:'short'}} -
              {{currentPrediction.validUntil | date:'short'}}
            </p>
          </div>
        </div>

        <!-- Natural Language Explanation -->
        <div class="prediction-explanation">
          <h3>📖 Prediction Explanation</h3>
          <div class="explanation-content">
            <p class="explanation-text">{{getPredictionExplanation()}}</p>
          </div>
        </div>

        <div class="risk-factors">
          <h3>🔍 Risk Factors</h3>
          <div class="factors-grid">
            <div class="factor-card" *ngFor="let factor of currentPrediction.factors">
              <div class="factor-header">
                <h4>{{getFactorTitle(factor.type)}}</h4>
                <div class="factor-value" [class]="getFactorLevel(factor.value)">
                  {{(factor.value * 100).toFixed(0)}}%
                </div>
              </div>
              <div class="factor-description">{{factor.description}}</div>
              <div class="factor-weight">Weight: {{(factor.weight * 100).toFixed(0)}}%</div>
              <div class="factor-explanation">
                <p>{{getFactorExplanation(factor)}}</p>
              </div>
            </div>
          </div>
        </div>

        <div class="recommendations">
          <h3>💡 Recommendations</h3>
          <div class="recommendation-list">
            <div class="recommendation-item" *ngFor="let recommendation of currentPrediction.recommendations">
              <div class="recommendation-icon">•</div>
              <div class="recommendation-text">{{recommendation}}</div>
            </div>
          </div>
        </div>
      </div>

      <ng-template #noPrediction>
        <div class="no-prediction">
          <div class="placeholder-icon">🔮</div>
          <h2>No Prediction Available</h2>
          <p>Generate a BCTV risk prediction for your current location or a specific area.</p>
          <p>The prediction engine analyzes:</p>
          <ul>
            <li>Host plant density and distribution</li>
            <li>Beet leafhopper populations</li>
            <li>Weather conditions</li>
            <li>Seasonal factors</li>
            <li>Historical outbreak data</li>
          </ul>
        </div>
      </ng-template>

      <div class="error-message" *ngIf="errorMessage">
        {{errorMessage}}
      </div>
    </div>
  `,
  styles: [`
    .prediction-container {
      max-width: 1000px;
      margin: 0 auto;
      padding: var(--space-4);
      background: var(--gray-50);
      min-height: 100vh;
    }

    /* Prediction Explanation Styles */
    .prediction-explanation {
      background: white;
      padding: var(--space-8);
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--gray-200);
      margin-bottom: var(--space-6);
    }

    .prediction-explanation h3 {
      font-size: var(--text-xl);
      font-weight: 600;
      color: var(--gray-900);
      margin: 0 0 var(--space-6) 0;
    }

    .explanation-content {
      background: var(--primary-50);
      border: 1px solid var(--primary-200);
      border-radius: var(--radius-lg);
      padding: var(--space-6);
    }

    .explanation-text {
      font-size: var(--text-base);
      line-height: 1.7;
      color: var(--gray-800);
      margin: 0;
    }

    .factor-explanation {
      margin-top: var(--space-4);
      padding: var(--space-4);
      background: var(--gray-50);
      border-radius: var(--radius-md);
      border-left: 3px solid var(--primary-500);
    }

    .factor-explanation p {
      font-size: var(--text-sm);
      line-height: 1.6;
      color: var(--gray-700);
      margin: 0;
      font-style: italic;
    }

    .prediction-header {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      margin-bottom: 2rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .prediction-header h1 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 1.75rem;
      font-weight: 700;
    }

    .prediction-header p {
      margin: 0 0 1rem 0;
      color: #666;
      font-size: 1rem;
    }

    .back-btn {
      background: #6c757d;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.875rem;
      transition: background-color 0.2s;
    }

    .back-btn:hover {
      background: #545b62;
    }

    .prediction-controls {
      display: flex;
      gap: 1rem;
      margin-bottom: 2rem;
      flex-wrap: wrap;
    }

    .location-selection {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      margin-bottom: 2rem;
    }

    .location-selection h3 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1.25rem;
    }

    .prediction-map {
      height: 400px;
      width: 100%;
      border-radius: 8px;
      border: 1px solid #ddd;
      margin: 1rem 0;
    }

    .selected-location {
      background: #f8f9fa;
      padding: 1rem;
      border-radius: 6px;
      border-left: 4px solid #007bff;
      margin-top: 1rem;
    }

    .selected-location p {
      margin: 0;
      color: #333;
      font-weight: 500;
    }

    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 4px;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .btn-primary {
      background: #007bff;
      color: white;
    }

    .btn-primary:hover:not(:disabled) {
      background: #0056b3;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover:not(:disabled) {
      background: #545b62;
    }

    .btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }

    .prediction-content {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }

    .risk-overview {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
    }

    .risk-level-card {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      text-align: center;
    }

    .risk-level-card.very_high { border-left: 6px solid #dc3545; }
    .risk-level-card.high { border-left: 6px solid #fd7e14; }
    .risk-level-card.moderate { border-left: 6px solid #ffc107; }
    .risk-level-card.low { border-left: 6px solid #28a745; }
    .risk-level-card.very_low { border-left: 6px solid #6c757d; }

    .risk-level-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .risk-level-header h2 {
      margin: 0;
      color: #333;
      font-size: 1.25rem;
    }

    .risk-score {
      font-size: 2rem;
      font-weight: 700;
      color: #007bff;
    }

    .risk-level-badge {
      font-size: 1.5rem;
      font-weight: 600;
      text-transform: uppercase;
      margin-bottom: 1rem;
    }

    .confidence {
      font-size: 0.875rem;
      color: #666;
    }

    .location-info {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .location-info h3 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1.25rem;
    }

    .validity {
      font-size: 0.875rem;
      color: #666;
      margin-top: 0.5rem;
    }

    .risk-factors {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .risk-factors h3 {
      margin: 0 0 1.5rem 0;
      color: #333;
      font-size: 1.25rem;
    }

    .factors-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1rem;
    }

    .factor-card {
      border: 1px solid #ddd;
      border-radius: 6px;
      padding: 1rem;
    }

    .factor-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;
    }

    .factor-header h4 {
      margin: 0;
      font-size: 0.875rem;
      font-weight: 600;
      color: #333;
    }

    .factor-value {
      font-weight: 600;
      padding: 0.25rem 0.5rem;
      border-radius: 12px;
      font-size: 0.75rem;
    }

    .factor-value.high { background: #dc3545; color: white; }
    .factor-value.medium { background: #ffc107; color: #333; }
    .factor-value.low { background: #28a745; color: white; }

    .factor-description {
      font-size: 0.75rem;
      color: #666;
      margin-bottom: 0.5rem;
    }

    .factor-weight {
      font-size: 0.625rem;
      color: #999;
    }

    .recommendations {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .recommendations h3 {
      margin: 0 0 1.5rem 0;
      color: #333;
      font-size: 1.25rem;
    }

    .recommendation-list {
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }

    .recommendation-item {
      display: flex;
      gap: 0.75rem;
      align-items: flex-start;
    }

    .recommendation-icon {
      color: #007bff;
      font-weight: 600;
      margin-top: 0.125rem;
    }

    .recommendation-text {
      flex: 1;
      color: #333;
      line-height: 1.5;
    }

    .no-prediction {
      background: white;
      padding: 3rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      text-align: center;
    }

    .placeholder-icon {
      font-size: 4rem;
      margin-bottom: 1rem;
    }

    .no-prediction h2 {
      color: #333;
      margin-bottom: 1rem;
    }

    .no-prediction ul {
      text-align: left;
      max-width: 400px;
      margin: 1.5rem auto;
    }

    .no-prediction li {
      margin-bottom: 0.5rem;
      color: #666;
    }

    .error-message {
      background: #fee;
      color: #c33;
      padding: 1rem;
      border-radius: 4px;
      border: 1px solid #fcc;
      margin-top: 1rem;
    }

    @media (max-width: 768px) {
      .prediction-container {
        padding: 1rem;
      }

      .risk-overview {
        grid-template-columns: 1fr;
      }

      .factors-grid {
        grid-template-columns: 1fr;
      }

      .prediction-controls {
        flex-direction: column;
      }
    }
  `]
})
export class PredictionViewComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('mapContainer', { static: false }) mapContainer!: ElementRef;

  currentPrediction: BCTVPrediction | null = null;
  isGenerating = false;
  isGettingLocation = false;
  errorMessage = '';
  selectedLocation: { latitude: number; longitude: number } | null = null;

  private map!: Map;
  private locationMarker: Marker | null = null;

  constructor(
    private predictionService: PredictionService,
    private geolocationService: GeolocationService,
    private supabaseService: SupabaseService,
    private router: Router
  ) {}

  ngOnInit() {}

  ngAfterViewInit() {
    if (this.mapContainer) {
      this.initializeMap();
    }
  }

  ngOnDestroy() {
    if (this.map) {
      this.map.remove();
    }
  }

  private initializeMap() {
    try {
      this.map = new Map({
        container: this.mapContainer.nativeElement,
        style: environment.maplibre.style,
        center: [-119.4179, 36.7783], // Central California
        zoom: 4.0, // Consistent with dashboard - entire California visible immediately
        attributionControl: false
      });

      // Add navigation controls
      this.map.addControl(new NavigationControl(), 'top-right');

      // Add click handler for location selection
      this.map.on('click', (e) => {
        this.selectLocation(e.lngLat.lat, e.lngLat.lng);
      });

      // Load existing observations when map is ready
      this.map.on('load', () => {
        this.loadExistingObservations();
      });

    } catch (error) {
      console.error('Error initializing map:', error);
    }
  }

  private selectLocation(latitude: number, longitude: number) {
    this.selectedLocation = { latitude, longitude };

    // Remove existing marker
    if (this.locationMarker) {
      this.locationMarker.remove();
    }

    // Add new marker
    this.locationMarker = new Marker({ color: '#007bff' })
      .setLngLat([longitude, latitude])
      .addTo(this.map);

    console.log('Selected location:', this.selectedLocation);
  }

  private async loadExistingObservations() {
    try {
      console.log('Loading existing observations for prediction map...');

      const { data: observations, error } = await this.supabaseService.db
        .from('observations')
        .select('*')
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false });

      if (error) throw error;

      console.log('Loaded observations:', observations?.length || 0);

      // Add markers for each observation
      if (observations && observations.length > 0) {
        observations.forEach(obs => {
          this.addObservationMarker(obs);
        });
      }

    } catch (error) {
      console.error('Error loading existing observations:', error);
    }
  }

  private addObservationMarker(observation: any) {
    try {
      const lat = parseFloat(observation.latitude);
      const lng = parseFloat(observation.longitude);

      if (isNaN(lat) || isNaN(lng)) {
        console.error('Invalid coordinates:', { lat: observation.latitude, lng: observation.longitude });
        return;
      }

      const color = this.getMarkerColor(observation.type);

      const popup = new Popup({ offset: 25 }).setHTML(`
        <div class="observation-popup">
          <h4>${this.getObservationTitle(observation.type)}</h4>
          <p><strong>Date:</strong> ${new Date(observation.created_at).toLocaleDateString()}</p>
          <p><strong>Location:</strong> ${lat.toFixed(4)}, ${lng.toFixed(4)}</p>
          ${observation.notes ? `<p><strong>Notes:</strong> ${observation.notes.substring(0, 100)}${observation.notes.length > 100 ? '...' : ''}</p>` : ''}
          <button onclick="window.location.href='/activity-details/${observation.id}'" class="popup-details-btn">
            View Details →
          </button>
        </div>
      `);

      const marker = new Marker({ color, scale: 0.8 })
        .setLngLat([lng, lat])
        .setPopup(popup)
        .addTo(this.map);

    } catch (error) {
      console.error('Error creating observation marker:', error);
    }
  }

  private getMarkerColor(type: string): string {
    switch (type) {
      case 'host_plant': return '#28a745';
      case 'blh_observation': return '#ffc107';
      case 'bctv_symptoms': return '#dc3545';
      case 'eradication_effort': return '#007bff';
      default: return '#6c757d';
    }
  }

  private getObservationTitle(type: string): string {
    switch (type) {
      case 'host_plant': return 'Host Plant';
      case 'blh_observation': return 'BLH Observation';
      case 'bctv_symptoms': return 'BCTV Symptoms';
      case 'eradication_effort': return 'Eradication Effort';
      default: return 'Observation';
    }
  }

  async generatePrediction() {
    this.isGenerating = true;
    this.errorMessage = '';

    try {
      // Get current location first
      const location = await this.geolocationService.getCurrentPosition().toPromise();

      if (!location) {
        throw new Error('Could not get current location');
      }

      const request: PredictionRequest = {
        location,
        radius: 10, // 10km radius
        timeframe: 7 // 7 days
      };

      this.predictionService.generatePrediction(request).subscribe({
        next: (prediction) => {
          this.currentPrediction = prediction;
          this.isGenerating = false;
        },
        error: (error) => {
          console.error('Prediction error:', error);
          this.errorMessage = 'Failed to generate prediction. Please try again.';
          this.isGenerating = false;
        }
      });

    } catch (error) {
      console.error('Location error:', error);
      this.errorMessage = 'Could not get location. Please enable location services.';
      this.isGenerating = false;
    }
  }

  getCurrentLocation() {
    this.isGettingLocation = true;
    this.errorMessage = '';

    this.geolocationService.getCurrentPosition().subscribe({
      next: (location) => {
        console.log('Current location:', location);
        this.selectedLocation = location;

        // Update map to show current location
        if (this.map) {
          this.selectLocation(location.latitude, location.longitude);
          this.map.flyTo({
            center: [location.longitude, location.latitude],
            zoom: 12
          });
        }

        this.isGettingLocation = false;
      },
      error: (error) => {
        console.error('Location error:', error);
        this.errorMessage = 'Could not get current location.';
        this.isGettingLocation = false;
      }
    });
  }

  async generatePredictionForSelectedLocation() {
    if (!this.selectedLocation) {
      this.errorMessage = 'Please select a location on the map first.';
      return;
    }

    this.isGenerating = true;
    this.errorMessage = '';

    try {
      const request: PredictionRequest = {
        location: this.selectedLocation,
        radius: 10, // 10km radius
        timeframe: 7 // 7 days
      };

      this.predictionService.generatePrediction(request).subscribe({
        next: (prediction) => {
          this.currentPrediction = prediction;
          this.isGenerating = false;
        },
        error: (error) => {
          console.error('Prediction error:', error);
          this.errorMessage = 'Failed to generate prediction. Please try again.';
          this.isGenerating = false;
        }
      });

    } catch (error) {
      console.error('Prediction error:', error);
      this.errorMessage = 'Failed to generate prediction. Please try again.';
      this.isGenerating = false;
    }
  }

  getRiskLevelDisplay(level: RiskLevel): string {
    switch (level) {
      case RiskLevel.VERY_HIGH: return 'Very High';
      case RiskLevel.HIGH: return 'High';
      case RiskLevel.MODERATE: return 'Moderate';
      case RiskLevel.LOW: return 'Low';
      case RiskLevel.VERY_LOW: return 'Very Low';
      default: return 'Unknown';
    }
  }

  getFactorTitle(type: string): string {
    switch (type) {
      case 'host_plant_density': return 'Host Plant Density';
      case 'blh_population': return 'BLH Population';
      case 'weather_conditions': return 'Weather Conditions';
      case 'seasonal_factors': return 'Seasonal Factors';
      case 'historical_outbreaks': return 'Historical Outbreaks';
      default: return type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  }

  getFactorLevel(value: number): string {
    if (value >= 0.7) return 'high';
    if (value >= 0.4) return 'medium';
    return 'low';
  }

  goBack() {
    this.router.navigate(['/dashboard']);
  }

  // Natural language explanation methods
  getPredictionExplanation(): string {
    if (!this.currentPrediction) return '';

    const riskLevel = this.currentPrediction.riskLevel;
    const riskScore = this.currentPrediction.riskScore;
    const location = this.currentPrediction.location;
    const confidence = Math.round(this.currentPrediction.confidence * 100);

    let explanation = '';

    // Base explanation based on risk level
    switch (riskLevel) {
      case 'very_high':
        explanation = `Our analysis indicates a very high risk (${riskScore}/100) of BCTV outbreak in this area. `;
        explanation += `This means there is a strong likelihood of virus transmission and crop damage. `;
        explanation += `Immediate preventive measures and close monitoring are strongly recommended.`;
        break;
      case 'high':
        explanation = `The prediction shows a high risk (${riskScore}/100) of BCTV occurrence in this location. `;
        explanation += `Conditions are favorable for virus spread, and proactive management strategies should be implemented. `;
        explanation += `Regular field inspections and early intervention measures are advised.`;
        break;
      case 'moderate':
        explanation = `A moderate risk (${riskScore}/100) of BCTV has been identified for this area. `;
        explanation += `While not immediately critical, conditions could support virus development. `;
        explanation += `Maintain regular monitoring and be prepared to implement control measures if conditions worsen.`;
        break;
      case 'low':
        explanation = `The analysis shows a low risk (${riskScore}/100) of BCTV in this region. `;
        explanation += `Current conditions are not highly favorable for virus transmission, but basic monitoring should continue. `;
        explanation += `This is a good time for preventive measures and host plant management.`;
        break;
      case 'very_low':
        explanation = `Very low risk (${riskScore}/100) of BCTV outbreak is predicted for this location. `;
        explanation += `Environmental conditions are currently unfavorable for virus spread. `;
        explanation += `Continue routine monitoring and maintain good agricultural practices.`;
        break;
    }

    // Add confidence and validity information
    explanation += ` This prediction has a ${confidence}% confidence level and is valid until ${this.currentPrediction.validUntil.toLocaleDateString()}.`;

    // Add location-specific context
    explanation += ` The assessment is based on current conditions at coordinates ${location.latitude.toFixed(4)}, ${location.longitude.toFixed(4)}`;
    if (location.address) {
      explanation += ` (${location.address})`;
    }
    explanation += '.';

    return explanation;
  }

  getFactorExplanation(factor: any): string {
    const value = Math.round(factor.value * 100);
    const weight = Math.round(factor.weight * 100);

    switch (factor.type) {
      case 'host_plant_density':
        if (value >= 70) {
          return `High host plant density (${value}%) significantly increases BCTV risk as it provides abundant food sources for beet leafhoppers and virus reservoirs. This factor contributes ${weight}% to the overall risk assessment.`;
        } else if (value >= 40) {
          return `Moderate host plant density (${value}%) creates some risk for BCTV transmission. While not critical, monitoring is recommended. This factor accounts for ${weight}% of the risk calculation.`;
        } else {
          return `Low host plant density (${value}%) reduces BCTV risk as fewer host plants limit leafhopper populations and virus spread. This protective factor represents ${weight}% of the assessment.`;
        }

      case 'blh_population':
        if (value >= 70) {
          return `High beet leafhopper populations (${value}%) pose a significant threat as these insects are the primary vectors for BCTV transmission. This critical factor weighs ${weight}% in the risk evaluation.`;
        } else if (value >= 40) {
          return `Moderate leafhopper activity (${value}%) indicates potential for virus transmission. Population levels should be monitored closely. This factor contributes ${weight}% to the overall risk.`;
        } else {
          return `Low leafhopper populations (${value}%) reduce transmission risk significantly. Fewer vectors mean less chance of virus spread. This factor accounts for ${weight}% of the assessment.`;
        }

      case 'weather_conditions':
        if (value >= 70) {
          return `Current weather conditions (${value}% favorable) strongly support BCTV development and leafhopper activity. Temperature, humidity, and wind patterns are optimal for virus transmission. This environmental factor contributes ${weight}% to the risk.`;
        } else if (value >= 40) {
          return `Weather conditions (${value}% favorable) are moderately supportive of BCTV activity. Some environmental factors may limit virus development. This contributes ${weight}% to the overall assessment.`;
        } else {
          return `Unfavorable weather conditions (${value}% favorable) help suppress BCTV activity and leafhopper populations. Environmental factors are working against virus transmission, accounting for ${weight}% of the protective assessment.`;
        }

      case 'seasonal_factors':
        if (value >= 70) {
          return `Peak seasonal conditions (${value}% risk) align with typical BCTV outbreak periods. This time of year historically shows increased virus activity. Seasonal timing contributes ${weight}% to the risk evaluation.`;
        } else if (value >= 40) {
          return `Moderate seasonal risk (${value}%) indicates some historical precedent for BCTV activity during this period. Seasonal patterns account for ${weight}% of the assessment.`;
        } else {
          return `Low seasonal risk (${value}%) suggests this time period typically has reduced BCTV activity based on historical data. Seasonal protection contributes ${weight}% to the favorable assessment.`;
        }

      case 'historical_outbreaks':
        if (value >= 70) {
          return `Strong historical precedent (${value}%) shows this area has experienced significant BCTV outbreaks in the past. Previous outbreak patterns contribute ${weight}% to the current risk assessment.`;
        } else if (value >= 40) {
          return `Moderate historical activity (${value}%) indicates some past BCTV occurrences in this region. Historical data accounts for ${weight}% of the risk evaluation.`;
        } else {
          return `Limited historical outbreaks (${value}%) suggest this area has been relatively protected from BCTV in the past. This favorable history contributes ${weight}% to the assessment.`;
        }

      default:
        return `This factor shows a ${value}% risk level and contributes ${weight}% to the overall prediction model.`;
    }
  }
}
