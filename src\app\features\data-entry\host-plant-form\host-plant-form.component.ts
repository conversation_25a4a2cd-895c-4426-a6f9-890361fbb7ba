import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { SupabaseService } from '../../../core/services/supabase.service';
import { AuthService } from '../../../core/services/auth.service';
import { MapNavigationService } from '../../../core/services/map-navigation.service';
import {
  HostPlantSpecies,
  PlantDensity,
  PlantHealthStatus,
  PlantGrowthStage,
  ObservationType,
  GeoLocation
} from '../../../core/models/observation.model';
import { PhotoUploadComponent } from '../../../shared/components/photo-upload/photo-upload.component';
import { LocationPickerComponent } from '../../../shared/components/location-picker/location-picker.component';
import { LoadingSpinnerComponent } from '../../../shared/components/loading-spinner/loading-spinner.component';

@Component({
  selector: 'app-host-plant-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    PhotoUploadComponent,
    LocationPickerComponent,
    LoadingSpinnerComponent
  ],
  template: `
    <div class="form-container">
      <div class="form-header">
        <h1>Host Plant Observation</h1>
        <p>Record host plant species and density information</p>
        <button (click)="goBack()" class="back-btn">← Back to Dashboard</button>
      </div>

      <form [formGroup]="hostPlantForm" (ngSubmit)="onSubmit()" class="observation-form">
        <!-- Location Section -->
        <div class="form-section">
          <h3>📍 Location Information</h3>
          <app-location-picker
            [required]="true"
            (locationSelected)="onLocationSelected($event)"
            (locationCleared)="onLocationCleared()">
          </app-location-picker>
        </div>

        <!-- Host Plant Details -->
        <div class="form-section">
          <h3>🌿 Host Plant Details</h3>

          <div class="form-row">
            <div class="form-group">
              <label for="species">Plant Species *</label>
              <select
                id="species"
                formControlName="species"
                class="form-control"
                [class.error]="isFieldInvalid('species')">
                <option value="">Select species</option>
                <option value="russian_thistle">Russian Thistle (Salsola tragus)</option>
                <option value="kochia">Kochia (Bassia scoparia)</option>
                <option value="lambsquarters">Lambsquarters (Chenopodium album)</option>
                <option value="pigweed">Pigweed (Amaranthus spp.)</option>
                <option value="shepherds_purse">Shepherd's Purse (Capsella bursa-pastoris)</option>
                <option value="london_rocket">London Rocket (Sisymbrium irio)</option>
                <option value="prickly_lettuce">Prickly Lettuce (Lactuca serriola)</option>
                <option value="mustard">Mustard (Brassica spp.)</option>
                <option value="filaree">Filaree (Erodium cicutarium)</option>
                <option value="malva">Malva (Malva spp.)</option>
              </select>
              <div class="error-message" *ngIf="isFieldInvalid('species')">
                Plant species is required
              </div>
            </div>

            <div class="form-group">
              <label for="density">Plant Density *</label>
              <select
                id="density"
                formControlName="density"
                class="form-control"
                [class.error]="isFieldInvalid('density')">
                <option value="">Select density</option>
                <option value="low">Low (< 10 plants/m²)</option>
                <option value="medium">Medium (10-50 plants/m²)</option>
                <option value="high">High (50-100 plants/m²)</option>
                <option value="very_high">Very High (> 100 plants/m²)</option>
              </select>
              <div class="error-message" *ngIf="isFieldInvalid('density')">
                Plant density is required
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="healthStatus">Health Status *</label>
              <select
                id="healthStatus"
                formControlName="healthStatus"
                class="form-control"
                [class.error]="isFieldInvalid('healthStatus')">
                <option value="">Select health status</option>
                <option value="healthy">Healthy</option>
                <option value="stressed">Stressed</option>
                <option value="diseased">Diseased</option>
                <option value="dying">Dying</option>
              </select>
              <div class="error-message" *ngIf="isFieldInvalid('healthStatus')">
                Health status is required
              </div>
            </div>

            <div class="form-group">
              <label for="growthStage">Growth Stage *</label>
              <select
                id="growthStage"
                formControlName="growthStage"
                class="form-control"
                [class.error]="isFieldInvalid('growthStage')">
                <option value="">Select growth stage</option>
                <option value="seedling">Seedling</option>
                <option value="vegetative">Vegetative</option>
                <option value="flowering">Flowering</option>
                <option value="fruiting">Fruiting</option>
                <option value="senescent">Senescent</option>
              </select>
              <div class="error-message" *ngIf="isFieldInvalid('growthStage')">
                Growth stage is required
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="estimatedCount">Estimated Plant Count</label>
              <input
                id="estimatedCount"
                type="number"
                formControlName="estimatedCount"
                class="form-control"
                placeholder="Approximate number of plants"
                min="0">
            </div>

            <div class="form-group">
              <label for="coverageArea">Coverage Area (m²)</label>
              <input
                id="coverageArea"
                type="number"
                formControlName="coverageArea"
                class="form-control"
                placeholder="Area covered by plants"
                min="0"
                step="0.1">
            </div>
          </div>
        </div>

        <!-- Additional Information -->
        <div class="form-section">
          <h3>📝 Additional Information</h3>

          <div class="form-group">
            <label for="notes">Notes</label>
            <textarea
              id="notes"
              formControlName="notes"
              class="form-control"
              rows="4"
              placeholder="Additional observations, environmental conditions, etc.">
            </textarea>
          </div>
        </div>

        <!-- Photo Upload -->
        <div class="form-section">
          <h3>📷 Photos</h3>
          <app-photo-upload
            #photoUpload
            [maxFiles]="5"
            [bucket]="'host-plant-observations'"
            (photosChanged)="onPhotosChanged($event)">
          </app-photo-upload>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <button
            type="button"
            class="btn btn-secondary"
            (click)="goBack()">
            Cancel
          </button>

          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="hostPlantForm.invalid || isSubmitting || !selectedLocation">
            <span *ngIf="!isSubmitting">Save Observation</span>
            <app-loading-spinner *ngIf="isSubmitting" size="small"></app-loading-spinner>
          </button>
        </div>

        <div class="error-message" *ngIf="submitError">
          {{submitError}}
        </div>

        <div class="success-message" *ngIf="submitSuccess">
          Host plant observation saved successfully!
        </div>
      </form>
    </div>
  `,
  styles: [`
    .form-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
      background: #f8f9fa;
      min-height: 100vh;
    }

    .form-header {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      margin-bottom: 2rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .form-header h1 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 1.75rem;
      font-weight: 700;
    }

    .form-header p {
      margin: 0 0 1rem 0;
      color: #666;
      font-size: 1rem;
    }

    .back-btn {
      background: #6c757d;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.875rem;
      transition: background-color 0.2s;
    }

    .back-btn:hover {
      background: #545b62;
    }

    .observation-form {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }

    .form-section {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .form-section h3 {
      margin: 0 0 1.5rem 0;
      color: #333;
      font-size: 1.25rem;
      font-weight: 600;
      border-bottom: 2px solid #007bff;
      padding-bottom: 0.5rem;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      margin-bottom: 1.5rem;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group label {
      font-weight: 500;
      color: #333;
      margin-bottom: 0.5rem;
      font-size: 0.875rem;
    }

    .form-control {
      padding: 0.75rem;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 0.875rem;
      transition: border-color 0.2s, box-shadow 0.2s;
    }

    .form-control:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
    }

    .form-control.error {
      border-color: #dc3545;
    }

    .form-control.error:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    }

    textarea.form-control {
      resize: vertical;
      min-height: 100px;
    }

    .error-message {
      color: #dc3545;
      font-size: 0.75rem;
      margin-top: 0.25rem;
    }

    .success-message {
      color: #28a745;
      font-size: 0.875rem;
      margin-top: 1rem;
      padding: 0.75rem;
      background: #d4edda;
      border: 1px solid #c3e6cb;
      border-radius: 4px;
    }

    .form-actions {
      display: flex;
      gap: 1rem;
      justify-content: flex-end;
      padding: 2rem;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 4px;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .btn-primary {
      background: #007bff;
      color: white;
    }

    .btn-primary:hover:not(:disabled) {
      background: #0056b3;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #545b62;
    }

    .btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }

    @media (max-width: 768px) {
      .form-container {
        padding: 1rem;
      }

      .form-header,
      .form-section,
      .form-actions {
        padding: 1.5rem;
      }

      .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .form-actions {
        flex-direction: column;
      }
    }
  `]
})
export class HostPlantFormComponent implements OnInit {
  @ViewChild('photoUpload') photoUpload!: PhotoUploadComponent;

  hostPlantForm: FormGroup;
  selectedLocation: GeoLocation | null = null;
  selectedPhotos: File[] = [];
  isSubmitting = false;
  submitError = '';
  submitSuccess = false;

  constructor(
    private fb: FormBuilder,
    private supabaseService: SupabaseService,
    private authService: AuthService,
    private mapNavigationService: MapNavigationService,
    private router: Router
  ) {
    this.hostPlantForm = this.fb.group({
      species: ['', Validators.required],
      density: ['', Validators.required],
      healthStatus: ['', Validators.required],
      growthStage: ['', Validators.required],
      estimatedCount: [''],
      coverageArea: [''],
      notes: ['']
    });
  }

  ngOnInit() {}

  onLocationSelected(location: GeoLocation) {
    this.selectedLocation = location;
  }

  onLocationCleared() {
    this.selectedLocation = null;
  }

  onPhotosChanged(photos: File[]) {
    this.selectedPhotos = photos;
  }

  async onSubmit() {
    if (this.hostPlantForm.valid && this.selectedLocation) {
      this.isSubmitting = true;
      this.submitError = '';
      this.submitSuccess = false;

      try {
        const currentUser = this.authService.currentUser;
        if (!currentUser) {
          throw new Error('User not authenticated');
        }

        // Upload photos first if any
        let photoUrls: string[] = [];
        if (this.selectedPhotos.length > 0) {
          photoUrls = await this.photoUpload.uploadPhotos();
        }

        const formValue = this.hostPlantForm.value;

        // Create observation record with full user information
        const observation = {
          user_id: currentUser.id,
          user_name: `${currentUser.firstName || ''} ${currentUser.lastName || ''}`.trim() || currentUser.email,
          user_email: currentUser.email,
          type: ObservationType.HOST_PLANT,
          latitude: this.selectedLocation.latitude,
          longitude: this.selectedLocation.longitude,
          accuracy: this.selectedLocation.accuracy,
          address: this.selectedLocation.address,
          host_plant_data: {
            species: formValue.species,
            density: formValue.density,
            healthStatus: formValue.healthStatus,
            growthStage: formValue.growthStage,
            estimatedCount: formValue.estimatedCount ? parseInt(formValue.estimatedCount) : null,
            coverageArea: formValue.coverageArea ? parseFloat(formValue.coverageArea) : null
          },
          photos: photoUrls,
          notes: formValue.notes,
          timestamp: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        const { data: insertedData, error } = await this.supabaseService.db
          .from('observations')
          .insert(observation)
          .select('id')
          .single();

        if (error) throw error;

        this.submitSuccess = true;

        // Navigate to dashboard and focus on the new observation
        setTimeout(() => {
          if (insertedData?.id && this.selectedLocation) {
            console.log('🎯 Navigating to new host plant observation:', insertedData.id);
            this.mapNavigationService.navigateToNewObservation(
              this.selectedLocation.latitude,
              this.selectedLocation.longitude,
              insertedData.id,
              'host_plant'
            );
          } else {
            this.router.navigate(['/dashboard']);
          }
        }, 2000);

      } catch (error) {
        console.error('Error saving observation:', error);
        this.submitError = 'Failed to save observation. Please try again.';
      } finally {
        this.isSubmitting = false;
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.hostPlantForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  private markFormGroupTouched() {
    Object.keys(this.hostPlantForm.controls).forEach(key => {
      const control = this.hostPlantForm.get(key);
      control?.markAsTouched();
    });
  }

  goBack() {
    this.router.navigate(['/dashboard']);
  }
}
