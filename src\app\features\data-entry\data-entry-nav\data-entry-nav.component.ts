import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';

@Component({
  selector: 'app-data-entry-nav',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <div class="data-entry-nav-container">
      <!-- Header -->
      <header class="nav-header">
        <div class="header-content">
          <h1>Data Entry</h1>
          <p>Choose the type of observation to record</p>
          <button (click)="goBack()" class="back-btn">← Back to Dashboard</button>
        </div>
      </header>

      <!-- Navigation Cards -->
      <main class="nav-main">
        <div class="nav-grid">
          <a routerLink="/data-entry/host-plant" class="nav-card">
            <div class="card-icon">🌿</div>
            <div class="card-content">
              <h3>Host Plant Observation</h3>
              <p>Record host plant species, density, and health status in the field</p>
              <div class="card-features">
                <span class="feature">• Species identification</span>
                <span class="feature">• Density assessment</span>
                <span class="feature">• Health monitoring</span>
                <span class="feature">• Photo documentation</span>
              </div>
            </div>
            <div class="card-arrow">→</div>
          </a>

          <a routerLink="/data-entry/blh" class="nav-card">
            <div class="card-icon">🦗</div>
            <div class="card-content">
              <h3>BLH Observation</h3>
              <p>Document Beet Leafhopper populations, behavior, and environmental conditions</p>
              <div class="card-features">
                <span class="feature">• Population counts</span>
                <span class="feature">• Behavior tracking</span>
                <span class="feature">• Weather conditions</span>
                <span class="feature">• Density mapping</span>
              </div>
            </div>
            <div class="card-arrow">→</div>
          </a>

          <a routerLink="/data-entry/bctv" class="nav-card">
            <div class="card-icon">🦠</div>
            <div class="card-content">
              <h3>BCTV Symptoms</h3>
              <p>Report Beet Curly Top Virus symptoms and affected plant assessments</p>
              <div class="card-features">
                <span class="feature">• Symptom identification</span>
                <span class="feature">• Severity assessment</span>
                <span class="feature">• Spread tracking</span>
                <span class="feature">• Impact analysis</span>
              </div>
            </div>
            <div class="card-arrow">→</div>
          </a>

          <a routerLink="/data-entry/eradication" class="nav-card">
            <div class="card-icon">🧹</div>
            <div class="card-content">
              <h3>Eradication Effort</h3>
              <p>Log weed control activities, methods used, and effectiveness measures</p>
              <div class="card-features">
                <span class="feature">• Control methods</span>
                <span class="feature">• Area coverage</span>
                <span class="feature">• Effectiveness rating</span>
                <span class="feature">• Follow-up planning</span>
              </div>
            </div>
            <div class="card-arrow">→</div>
          </a>
        </div>

        <!-- Quick Stats -->
        <section class="quick-stats">
          <h2>Recent Activity</h2>
          <div class="stats-grid">
            <div class="stat-card">
              <div class="stat-number">{{recentStats.hostPlants}}</div>
              <div class="stat-label">Host Plants Logged</div>
              <div class="stat-period">This Week</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{recentStats.blhObservations}}</div>
              <div class="stat-label">BLH Observations</div>
              <div class="stat-period">This Week</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{recentStats.bctvReports}}</div>
              <div class="stat-label">BCTV Reports</div>
              <div class="stat-period">This Week</div>
            </div>
            <div class="stat-card">
              <div class="stat-number">{{recentStats.eradicationEfforts}}</div>
              <div class="stat-label">Eradication Efforts</div>
              <div class="stat-period">This Week</div>
            </div>
          </div>
        </section>
      </main>
    </div>
  `,
  styles: [`
    .data-entry-nav-container {
      min-height: 100vh;
      background: var(--gray-50);
      display: flex;
      flex-direction: column;
    }

    .nav-header {
      background: white;
      border-bottom: 1px solid var(--gray-200);
      box-shadow: var(--shadow-sm);
      padding: var(--space-6) var(--space-4);
    }

    .header-content h1 {
      font-size: var(--text-2xl);
      font-weight: 700;
      color: var(--gray-900);
      margin: 0 0 var(--space-2) 0;
    }

    .header-content p {
      color: var(--gray-600);
      margin: 0 0 var(--space-4) 0;
    }

    .back-btn {
      background: var(--gray-100);
      color: var(--gray-700);
      border: 1px solid var(--gray-300);
      padding: var(--space-2) var(--space-4);
      border-radius: var(--radius-lg);
      text-decoration: none;
      font-size: var(--text-sm);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
    }

    .back-btn:hover {
      background: var(--gray-200);
      transform: translateY(-1px);
    }

    .nav-main {
      flex: 1;
      padding: var(--space-8) var(--space-4);
      max-width: 1200px;
      margin: 0 auto;
      width: 100%;
    }

    .nav-grid {
      display: grid;
      grid-template-columns: 1fr;
      gap: var(--space-6);
      margin-bottom: var(--space-12);
    }

    .nav-card {
      background: white;
      border: 1px solid var(--gray-200);
      border-radius: var(--radius-xl);
      padding: var(--space-8);
      text-decoration: none;
      color: inherit;
      transition: all var(--transition-fast);
      box-shadow: var(--shadow-sm);
      display: flex;
      align-items: center;
      gap: var(--space-6);
      position: relative;
      overflow: hidden;
    }

    .nav-card:hover {
      transform: translateY(-4px);
      box-shadow: var(--shadow-lg);
      border-color: var(--primary-300);
    }

    .nav-card:hover .card-arrow {
      transform: translateX(4px);
    }

    .card-icon {
      font-size: 3rem;
      flex-shrink: 0;
    }

    .card-content {
      flex: 1;
    }

    .card-content h3 {
      font-size: var(--text-xl);
      font-weight: 600;
      color: var(--gray-900);
      margin: 0 0 var(--space-3) 0;
    }

    .card-content p {
      color: var(--gray-600);
      margin: 0 0 var(--space-4) 0;
      line-height: 1.5;
    }

    .card-features {
      display: flex;
      flex-direction: column;
      gap: var(--space-1);
    }

    .feature {
      font-size: var(--text-sm);
      color: var(--gray-500);
    }

    .card-arrow {
      font-size: var(--text-2xl);
      color: var(--primary-600);
      transition: transform var(--transition-fast);
      flex-shrink: 0;
    }

    .quick-stats {
      background: white;
      border-radius: var(--radius-xl);
      padding: var(--space-8);
      box-shadow: var(--shadow-sm);
      border: 1px solid var(--gray-200);
    }

    .quick-stats h2 {
      font-size: var(--text-xl);
      font-weight: 600;
      color: var(--gray-900);
      margin: 0 0 var(--space-6) 0;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: var(--space-4);
    }

    .stat-card {
      text-align: center;
      padding: var(--space-6);
      background: var(--gray-50);
      border-radius: var(--radius-lg);
      border: 1px solid var(--gray-200);
    }

    .stat-number {
      font-size: var(--text-3xl);
      font-weight: 700;
      color: var(--primary-600);
      margin-bottom: var(--space-2);
    }

    .stat-label {
      font-size: var(--text-sm);
      font-weight: 500;
      color: var(--gray-900);
      margin-bottom: var(--space-1);
    }

    .stat-period {
      font-size: var(--text-xs);
      color: var(--gray-500);
    }

    @media (min-width: 768px) {
      .nav-header {
        padding: var(--space-8);
      }

      .nav-main {
        padding: var(--space-12) var(--space-8);
      }

      .nav-grid {
        grid-template-columns: repeat(2, 1fr);
      }

      .stats-grid {
        grid-template-columns: repeat(4, 1fr);
      }

      .card-features {
        flex-direction: row;
        flex-wrap: wrap;
        gap: var(--space-3);
      }

      .feature {
        flex: 1;
        min-width: 120px;
      }
    }

    @media (min-width: 1024px) {
      .nav-card {
        padding: var(--space-10);
      }

      .card-content h3 {
        font-size: var(--text-2xl);
      }
    }
  `]
})
export class DataEntryNavComponent {
  recentStats = {
    hostPlants: 12,
    blhObservations: 8,
    bctvReports: 3,
    eradicationEfforts: 5
  };

  constructor(private router: Router) {}

  goBack() {
    this.router.navigate(['/dashboard']);
  }
}
