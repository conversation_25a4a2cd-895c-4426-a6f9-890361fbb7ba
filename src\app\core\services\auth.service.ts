import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable, from, map, catchError, of, throwError, switchMap } from 'rxjs';
import { SupabaseService } from './supabase.service';
import { User, UserProfile, UserRole } from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  private profileLoadingCache = new Map<string, Promise<void>>();

  constructor(
    public supabaseService: SupabaseService,
    private router: Router
  ) {
    // Initialize auth state asynchronously to avoid blocking constructor
    this.initializeAuth();
  }

  private async initializeAuth() {
    try {
      console.log('🔐 Initializing authentication...');

      // Set up auth state change listener first
      this.supabaseService.auth.onAuthStateChange(async (event, session) => {
        console.log('🔄 Auth state changed:', event, session?.user?.email || 'No user');

        if (event === 'SIGNED_IN' && session?.user) {
          console.log('✅ User signed in via auth state change');
          this.setUserFromSession(session);
        } else if (event === 'SIGNED_OUT') {
          console.log('👋 User signed out');
          this.currentUserSubject.next(null);
        } else if (event === 'TOKEN_REFRESHED' && session?.user) {
          console.log('🔄 Token refreshed for user:', session.user.email);
          this.setUserFromSession(session);
        } else if (event === 'USER_UPDATED' && session?.user) {
          console.log('👤 User updated:', session.user.email);
          this.setUserFromSession(session);
        }
      });

      // Check for existing session without blocking timeout
      this.checkSession().catch(error => {
        console.log('⚠️ Session check failed during initialization:', error);
        // Don't block initialization if session check fails
      });

      console.log('✅ Auth initialization completed');
    } catch (error) {
      console.error('❌ Error initializing auth:', error);
      // Don't set user to null during initialization errors
    }
  }

  private async checkSession() {
    try {
      console.log('🔍 Checking existing session...');
      const { data: { session }, error } = await this.supabaseService.auth.getSession();

      if (error) {
        console.error('❌ Session check error:', error);
        return;
      }

      if (session?.user) {
        console.log('✅ Found existing session for user:', session.user.email);
        this.setUserFromSession(session);
      } else {
        console.log('ℹ️ No existing session found');
      }
    } catch (error) {
      console.error('❌ Error checking session:', error);
    }
  }

  private setUserFromSession(session: any): void {
    try {
      const user = session.user;
      if (!user) return;

      // Set user state immediately
      const basicUser: User = {
        id: user.id,
        email: user.email || '',
        role: user.user_metadata?.['role'] || UserRole.FIELD_WORKER,
        firstName: user.user_metadata?.['first_name'] || '',
        lastName: user.user_metadata?.['last_name'] || '',
        organization: user.user_metadata?.['organization'] || '',
        phone: user.user_metadata?.['phone'] || '',
        createdAt: new Date(user.created_at || Date.now()),
        updatedAt: new Date()
      };

      this.currentUserSubject.next(basicUser);
      console.log('✅ User state set from session:', basicUser.email);

      // Load profile in background (non-blocking)
      this.loadUserProfile(user.id).catch(err => {
        console.log('Background profile loading failed:', err);
      });
    } catch (error) {
      console.error('❌ Error setting user from session:', error);
    }
  }

  private loadUserProfileAsync(userId: string): void {
    // Check if already loading this profile
    if (this.profileLoadingCache.has(userId)) {
      return;
    }

    // Start loading and cache the promise
    const loadingPromise = this.loadUserProfile(userId);
    this.profileLoadingCache.set(userId, loadingPromise);

    // Clean up cache after completion
    loadingPromise.finally(() => {
      this.profileLoadingCache.delete(userId);
    }).catch(err => {
      console.error('Profile loading failed, but login succeeded:', err);
    });
  }

  private async loadUserProfile(userId: string): Promise<void> {
    try {
      console.log('📋 Loading user profile for:', userId);

      const { data: profile, error } = await this.supabaseService.db
        .from('user_profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        console.log('ℹ️ No database profile found, using auth metadata');

        // Get current user from auth to ensure we have the latest data
        const { data: { user }, error: authError } = await this.supabaseService.auth.getUser();

        if (!authError && user) {
          // Create/update user with auth metadata
          const basicUser: User = {
            id: userId,
            email: user.email || '',
            role: user.user_metadata?.['role'] || UserRole.FIELD_WORKER,
            firstName: user.user_metadata?.['first_name'] || '',
            lastName: user.user_metadata?.['last_name'] || '',
            organization: user.user_metadata?.['organization'] || '',
            phone: user.user_metadata?.['phone'] || '',
            createdAt: new Date(user.created_at || Date.now()),
            updatedAt: new Date()
          };

          console.log('✅ Set user from auth metadata:', basicUser.email);
          this.currentUserSubject.next(basicUser);
        }
        return;
      }

      // Update existing user with database profile data
      const currentUser = this.currentUserSubject.value;
      if (currentUser) {
        const enhancedUser: User = {
          ...currentUser,
          role: profile.role || currentUser.role,
          firstName: profile.first_name || currentUser.firstName,
          lastName: profile.last_name || currentUser.lastName,
          organization: profile.organization || currentUser.organization,
          phone: profile.phone || currentUser.phone,
          createdAt: new Date(profile.created_at),
          updatedAt: new Date(profile.updated_at)
        };

        console.log('✅ Enhanced user profile with database data:', enhancedUser.email);
        this.currentUserSubject.next(enhancedUser);
      } else {
        // Fallback: create user from profile if no current user (shouldn't happen)
        const user: User = {
          id: userId,
          email: profile.email,
          role: profile.role,
          firstName: profile.first_name,
          lastName: profile.last_name,
          organization: profile.organization,
          phone: profile.phone,
          createdAt: new Date(profile.created_at),
          updatedAt: new Date(profile.updated_at)
        };

        console.log('✅ Created user from profile:', user.email);
        this.currentUserSubject.next(user);
      }
    } catch (error) {
      console.error('❌ Error loading user profile (keeping basic user):', error);
      // Don't set user to null - keep the basic user that's already set
    }
  }

  signUp(email: string, password: string, userData: Partial<UserProfile>): Observable<any> {
    return from(
      this.supabaseService.auth.signUp({
        email,
        password,
        options: {
          data: {
            first_name: userData.firstName,
            last_name: userData.lastName,
            organization: userData.organization,
            phone: userData.phone,
            role: userData.role || UserRole.FIELD_WORKER
          }
        }
      })
    ).pipe(
      map(({ data, error }) => {
        if (error) throw error;
        return data;
      }),
      catchError(error => {
        console.error('Sign up error:', error);
        throw error;
      })
    );
  }

  signIn(email: string, password: string): Observable<any> {
    console.log('🔄 AuthService.signIn called for:', email);

    return from(
      this.supabaseService.auth.signInWithPassword({
        email,
        password
      })
    ).pipe(
      map(({ data, error }) => {
        console.log('📥 Supabase signIn response received');
        console.log('Data:', data);
        console.log('Error:', error);

        if (error) {
          console.error('❌ Supabase signIn error:', error);
          throw error;
        }

        if (data.user && data.session) {
          console.log('✅ Sign in successful, setting user state immediately');
          this.setUserFromSession(data.session);
          console.log('✅ signIn observable completing successfully');
          return { success: true, user: data.user };
        } else {
          console.error('❌ No user or session in response');
          throw new Error('No user or session returned from sign in');
        }
      }),
      catchError(error => {
        console.error('❌ Sign in error in observable:', error);
        throw error;
      })
    );
  }

  signOut(): Observable<any> {
    return from(this.supabaseService.auth.signOut()).pipe(
      map(({ error }) => {
        if (error) throw error;
        this.currentUserSubject.next(null);
        this.router.navigate(['/auth/login']);
        return true;
      }),
      catchError(error => {
        console.error('Sign out error:', error);
        throw error;
      })
    );
  }

  resetPassword(email: string): Observable<any> {
    return from(
      this.supabaseService.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`
      })
    ).pipe(
      map(({ error }) => {
        if (error) throw error;
        return true;
      }),
      catchError(error => {
        console.error('Reset password error:', error);
        throw error;
      })
    );
  }

  updatePassword(newPassword: string): Observable<any> {
    return from(
      this.supabaseService.auth.updateUser({
        password: newPassword
      })
    ).pipe(
      map(({ data, error }) => {
        if (error) throw error;
        return data;
      }),
      catchError(error => {
        console.error('Update password error:', error);
        throw error;
      })
    );
  }

  updateProfile(profileData: Partial<UserProfile>): Observable<any> {
    const currentUser = this.currentUserSubject.value;
    if (!currentUser) {
      throw new Error('No authenticated user');
    }

    return from(
      this.supabaseService.db
        .from('user_profiles')
        .update({
          first_name: profileData.firstName,
          last_name: profileData.lastName,
          organization: profileData.organization,
          phone: profileData.phone,
          updated_at: new Date().toISOString()
        })
        .eq('user_id', currentUser.id)
    ).pipe(
      map(({ error }) => {
        if (error) throw error;
        // Reload user profile
        this.loadUserProfile(currentUser.id);
        return true;
      }),
      catchError(error => {
        console.error('Update profile error:', error);
        throw error;
      })
    );
  }

  get currentUser(): User | null {
    return this.currentUserSubject.value;
  }

  get isAuthenticated(): boolean {
    return this.currentUserSubject.value !== null;
  }

  hasRole(role: UserRole): boolean {
    const user = this.currentUserSubject.value;
    return user ? user.role === role : false;
  }

  hasAnyRole(roles: UserRole[]): boolean {
    const user = this.currentUserSubject.value;
    return user ? roles.includes(user.role) : false;
  }
}
