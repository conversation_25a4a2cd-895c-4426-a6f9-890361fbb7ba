import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { GeolocationService } from '../../../core/services/geolocation.service';
import { GeoLocation } from '../../../core/models/observation.model';

@Component({
  selector: 'app-location-picker',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="location-picker">
      <div class="location-header">
        <h4>Location</h4>
        <button
          type="button"
          class="gps-btn"
          [disabled]="isGettingLocation"
          (click)="getCurrentLocation()">
          <span class="gps-icon">📍</span>
          {{isGettingLocation ? 'Getting location...' : 'Use GPS'}}
        </button>
      </div>

      <div class="location-input-group">
        <div class="coordinate-inputs">
          <div class="input-group">
            <label for="latitude">Latitude</label>
            <input
              id="latitude"
              type="number"
              step="any"
              [(ngModel)]="latitude"
              (ngModelChange)="onCoordinateChange()"
              placeholder="e.g., 37.7749"
              class="coordinate-input">
          </div>

          <div class="input-group">
            <label for="longitude">Longitude</label>
            <input
              id="longitude"
              type="number"
              step="any"
              [(ngModel)]="longitude"
              (ngModelChange)="onCoordinateChange()"
              placeholder="e.g., -122.4194"
              class="coordinate-input">
          </div>
        </div>

        <div class="accuracy-info" *ngIf="currentLocation?.accuracy">
          <span class="accuracy-label">Accuracy:</span>
          <span class="accuracy-value">±{{currentLocation?.accuracy?.toFixed(0)}}m</span>
        </div>
      </div>

      <div class="address-display" *ngIf="address">
        <div class="address-label">Address:</div>
        <div class="address-text">{{address}}</div>
      </div>

      <div class="location-validation" *ngIf="validationMessage">
        <div class="validation-message" [class.error]="!isValidLocation">
          {{validationMessage}}
        </div>
      </div>

      <div class="location-actions">
        <button
          type="button"
          class="action-btn secondary"
          (click)="clearLocation()">
          Clear
        </button>

        <button
          type="button"
          class="action-btn primary"
          [disabled]="!isValidLocation || isConfirming"
          (click)="confirmLocation()">
          <span *ngIf="!isConfirming && !showConfirmationSuccess">Confirm Location</span>
          <span *ngIf="isConfirming">Confirming...</span>
          <span *ngIf="showConfirmationSuccess">✓ Confirmed</span>
        </button>
      </div>

      <div class="confirmation-feedback" *ngIf="confirmationMessage">
        <div class="confirmation-message" [class.success]="showConfirmationSuccess" [class.error]="!showConfirmationSuccess">
          {{confirmationMessage}}
        </div>
      </div>
    </div>
  `,
  styles: [`
    .location-picker {
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 1rem;
      background: white;
    }

    .location-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .location-header h4 {
      margin: 0;
      color: #333;
    }

    .gps-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.875rem;
      transition: background-color 0.2s;
    }

    .gps-btn:hover:not(:disabled) {
      background: #0056b3;
    }

    .gps-btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }

    .gps-icon {
      font-size: 1rem;
    }

    .location-input-group {
      margin-bottom: 1rem;
    }

    .coordinate-inputs {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
      margin-bottom: 0.5rem;
    }

    .input-group {
      display: flex;
      flex-direction: column;
    }

    .input-group label {
      font-size: 0.875rem;
      font-weight: 500;
      color: #555;
      margin-bottom: 0.25rem;
    }

    .coordinate-input {
      padding: 0.5rem;
      border: 1px solid #ccc;
      border-radius: 4px;
      font-size: 0.875rem;
    }

    .coordinate-input:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    .accuracy-info {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-size: 0.75rem;
      color: #666;
    }

    .accuracy-label {
      font-weight: 500;
    }

    .accuracy-value {
      color: #28a745;
      font-weight: 600;
    }

    .address-display {
      margin-bottom: 1rem;
      padding: 0.75rem;
      background: #f8f9fa;
      border-radius: 4px;
      border-left: 4px solid #007bff;
    }

    .address-label {
      font-size: 0.75rem;
      font-weight: 600;
      color: #666;
      margin-bottom: 0.25rem;
    }

    .address-text {
      font-size: 0.875rem;
      color: #333;
      line-height: 1.4;
    }

    .location-validation {
      margin-bottom: 1rem;
    }

    .validation-message {
      padding: 0.5rem;
      border-radius: 4px;
      font-size: 0.875rem;
    }

    .validation-message.error {
      background: #fee;
      color: #c33;
      border: 1px solid #fcc;
    }

    .validation-message:not(.error) {
      background: #efe;
      color: #363;
      border: 1px solid #cfc;
    }

    .location-actions {
      display: flex;
      gap: 0.5rem;
      justify-content: flex-end;
    }

    .action-btn {
      padding: 0.5rem 1rem;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.875rem;
      font-weight: 500;
      transition: all 0.2s;
    }

    .action-btn.primary {
      background: #28a745;
      color: white;
    }

    .action-btn.primary:hover:not(:disabled) {
      background: #218838;
    }

    .action-btn.primary:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }

    .action-btn.secondary {
      background: #6c757d;
      color: white;
    }

    .action-btn.secondary:hover {
      background: #545b62;
    }

    .confirmation-feedback {
      margin-top: 0.75rem;
    }

    .confirmation-message {
      padding: 0.5rem;
      border-radius: 4px;
      font-size: 0.875rem;
      font-weight: 500;
      text-align: center;
    }

    .confirmation-message.success {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .confirmation-message.error {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    @media (max-width: 768px) {
      .location-picker {
        padding: 0.75rem;
      }

      .location-header {
        flex-direction: column;
        align-items: stretch;
        gap: 0.75rem;
      }

      .coordinate-inputs {
        grid-template-columns: 1fr;
        gap: 0.75rem;
      }

      .location-actions {
        flex-direction: column;
      }
    }
  `]
})
export class LocationPickerComponent implements OnInit, OnDestroy {
  @Input() initialLocation?: GeoLocation;
  @Input() required: boolean = true;
  @Output() locationSelected = new EventEmitter<GeoLocation>();
  @Output() locationCleared = new EventEmitter<void>();

  latitude: number | null = null;
  longitude: number | null = null;
  currentLocation: GeoLocation | null = null;
  address: string = '';
  isGettingLocation = false;
  validationMessage = '';
  isValidLocation = false;
  isConfirming = false;
  confirmationMessage = '';
  showConfirmationSuccess = false;

  constructor(private geolocationService: GeolocationService) {}

  ngOnInit() {
    if (this.initialLocation) {
      this.setLocation(this.initialLocation);
    }
  }

  ngOnDestroy() {}

  getCurrentLocation() {
    this.isGettingLocation = true;
    this.validationMessage = '';

    this.geolocationService.getCurrentPosition({
      enableHighAccuracy: true,
      timeout: 15000,
      maximumAge: 60000
    }).subscribe({
      next: (location) => {
        this.setLocation(location);
        this.isGettingLocation = false;
      },
      error: (error) => {
        console.error('Geolocation error:', error);
        this.validationMessage = error.message || 'Failed to get current location';
        this.isGettingLocation = false;
      }
    });
  }

  onCoordinateChange() {
    if (this.latitude !== null && this.longitude !== null) {
      const location: GeoLocation = {
        latitude: this.latitude,
        longitude: this.longitude
      };

      this.currentLocation = location;
      this.validateLocation();
      this.updateAddress();
    } else {
      this.currentLocation = null;
      this.isValidLocation = false;
      this.validationMessage = '';
      this.address = '';
    }
  }

  private setLocation(location: GeoLocation) {
    this.latitude = location.latitude;
    this.longitude = location.longitude;
    this.currentLocation = location;
    this.validateLocation();
    this.updateAddress();
  }

  private validateLocation() {
    if (!this.currentLocation) {
      this.isValidLocation = false;
      this.validationMessage = this.required ? 'Location is required' : '';
      return;
    }

    const { latitude, longitude } = this.currentLocation;

    // Basic coordinate validation
    if (latitude < -90 || latitude > 90) {
      this.isValidLocation = false;
      this.validationMessage = 'Latitude must be between -90 and 90';
      return;
    }

    if (longitude < -180 || longitude > 180) {
      this.isValidLocation = false;
      this.validationMessage = 'Longitude must be between -180 and 180';
      return;
    }

    // Check if location is within California (for BCTV application)
    if (!this.geolocationService.isWithinCalifornia(this.currentLocation)) {
      this.isValidLocation = false;
      this.validationMessage = 'Location must be within California';
      return;
    }

    this.isValidLocation = true;
    this.validationMessage = 'Valid location within California';
  }

  private async updateAddress() {
    if (!this.currentLocation) {
      this.address = '';
      return;
    }

    try {
      this.address = await this.geolocationService.reverseGeocode(
        this.currentLocation.latitude,
        this.currentLocation.longitude
      );
    } catch (error) {
      console.warn('Failed to get address:', error);
      this.address = `${this.currentLocation.latitude.toFixed(6)}, ${this.currentLocation.longitude.toFixed(6)}`;
    }
  }

  confirmLocation() {
    if (this.isValidLocation && this.currentLocation) {
      this.isConfirming = true;
      this.confirmationMessage = '';

      try {
        const locationWithAddress: GeoLocation = {
          ...this.currentLocation,
          address: this.address
        };

        // Simulate a brief confirmation process
        setTimeout(() => {
          this.locationSelected.emit(locationWithAddress);
          this.showConfirmationSuccess = true;
          this.confirmationMessage = 'Location confirmed successfully!';
          this.isConfirming = false;

          // Reset confirmation state after 3 seconds
          setTimeout(() => {
            this.showConfirmationSuccess = false;
            this.confirmationMessage = '';
          }, 3000);
        }, 500);

      } catch (error) {
        this.isConfirming = false;
        this.showConfirmationSuccess = false;
        this.confirmationMessage = 'Failed to confirm location. Please try again.';
        console.error('Location confirmation error:', error);
      }
    } else {
      this.confirmationMessage = 'Please ensure location is valid before confirming.';
      this.showConfirmationSuccess = false;
    }
  }

  clearLocation() {
    this.latitude = null;
    this.longitude = null;
    this.currentLocation = null;
    this.address = '';
    this.isValidLocation = false;
    this.validationMessage = '';
    this.confirmationMessage = '';
    this.showConfirmationSuccess = false;
    this.isConfirming = false;
    this.locationCleared.emit();
  }

  // Public method to get current location
  getLocation(): GeoLocation | null {
    return this.currentLocation;
  }

  // Public method to check if location is valid
  isLocationValid(): boolean {
    return this.isValidLocation;
  }
}
