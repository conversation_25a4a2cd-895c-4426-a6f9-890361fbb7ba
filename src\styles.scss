/* Modern BCTV Application Global Styles - 2025 Design System */

/* Import MapLibre GL CSS - Required for map markers and controls to display properly */
@import 'maplibre-gl/dist/maplibre-gl.css';

/* Modern Design System Variables */
:root {
  /* Primary Colors - Agricultural Green Palette */
  --primary-50: #f0f9f0;
  --primary-100: #dcf2dc;
  --primary-200: #bae5ba;
  --primary-300: #8dd48d;
  --primary-400: #5cb85c;
  --primary-500: #3a9b3a;
  --primary-600: #2d7d2d;
  --primary-700: #256325;
  --primary-800: #1f4f1f;
  --primary-900: #1a421a;

  /* Secondary Colors - Earth Tones */
  --secondary-50: #faf8f3;
  --secondary-100: #f4f0e6;
  --secondary-200: #e8dcc7;
  --secondary-300: #d9c5a0;
  --secondary-400: #c8a876;
  --secondary-500: #b8915a;
  --secondary-600: #a67c4e;
  --secondary-700: #8a6642;
  --secondary-800: #70533a;
  --secondary-900: #5c4530;

  /* Accent Colors */
  --accent-blue: #2563eb;
  --accent-orange: #ea580c;
  --accent-red: #dc2626;
  --accent-yellow: #ca8a04;

  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Typography */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;

  /* Font Sizes - Mobile First */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */

  /* Spacing - Mobile First */
  --space-1: 0.25rem;    /* 4px */
  --space-2: 0.5rem;     /* 8px */
  --space-3: 0.75rem;    /* 12px */
  --space-4: 1rem;       /* 16px */
  --space-5: 1.25rem;    /* 20px */
  --space-6: 1.5rem;     /* 24px */
  --space-8: 2rem;       /* 32px */
  --space-10: 2.5rem;    /* 40px */
  --space-12: 3rem;      /* 48px */
  --space-16: 4rem;      /* 64px */

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Global Reset and Base Styles */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--gray-900);
  background-color: var(--gray-50);
  overflow-x: hidden;
  -webkit-overflow-scrolling: touch;
  min-height: 100vh;
}

/* Modern Typography Scale */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.25;
  margin-bottom: var(--space-4);
  color: var(--gray-900);
}

h1 { font-size: var(--text-3xl); }
h2 { font-size: var(--text-2xl); }
h3 { font-size: var(--text-xl); }
h4 { font-size: var(--text-lg); }
h5 { font-size: var(--text-base); }
h6 { font-size: var(--text-sm); }

/* Responsive Typography */
@media (min-width: 768px) {
  h1 { font-size: var(--text-4xl); }
  h2 { font-size: var(--text-3xl); }
  h3 { font-size: var(--text-2xl); }
}

p {
  margin-bottom: var(--space-4);
  color: var(--gray-700);
}

/* Modern Button System */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-sm);
  font-weight: 500;
  line-height: 1;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  min-height: 44px; /* Touch target size */
  min-width: 44px;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &:focus {
    outline: 2px solid var(--primary-500);
    outline-offset: 2px;
  }
}

.btn-primary {
  background-color: var(--primary-600);
  color: white;
  border-color: var(--primary-600);

  &:hover:not(:disabled) {
    background-color: var(--primary-700);
    border-color: var(--primary-700);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  &:active {
    transform: translateY(0);
  }
}

.btn-secondary {
  background-color: white;
  color: var(--gray-700);
  border-color: var(--gray-300);

  &:hover:not(:disabled) {
    background-color: var(--gray-50);
    border-color: var(--gray-400);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-600);
  border-color: var(--primary-600);

  &:hover:not(:disabled) {
    background-color: var(--primary-600);
    color: white;
  }
}

/* Mobile-First Form Styles */
.form-container {
  width: 100%;
  max-width: 100%;
  padding: var(--space-4);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

@media (min-width: 768px) {
  .form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: var(--space-8);
  }
}

/* Modern Card Component */
.card {
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: all var(--transition-normal);

  &:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
  }
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.card-body {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-primary { color: var(--primary-600); }
.text-secondary { color: var(--gray-600); }
.text-success { color: var(--primary-600); }
.text-warning { color: var(--accent-yellow); }
.text-danger { color: var(--accent-red); }

.bg-primary { background-color: var(--primary-600); }
.bg-secondary { background-color: var(--gray-100); }
.bg-success { background-color: var(--primary-100); }
.bg-warning { background-color: #fef3c7; }
.bg-danger { background-color: #fee2e2; }

/* Mobile-First Responsive Utilities */
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }

@media (min-width: 640px) {
  .sm\:hidden { display: none; }
  .sm\:block { display: block; }
  .sm\:flex { display: flex; }
  .sm\:grid { display: grid; }
}

@media (min-width: 768px) {
  .md\:hidden { display: none; }
  .md\:block { display: block; }
  .md\:flex { display: flex; }
  .md\:grid { display: grid; }
}

@media (min-width: 1024px) {
  .lg\:hidden { display: none; }
  .lg\:block { display: block; }
  .lg\:flex { display: flex; }
  .lg\:grid { display: grid; }
}
