import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { SupabaseService } from '../../core/services/supabase.service';
import { LoadingSpinnerComponent } from '../../shared/components/loading-spinner/loading-spinner.component';

interface DataEntry {
  id: string;
  type: string;
  created_at: string;
  latitude: number;
  longitude: number;
  data: any;
  photos?: string[];
  user_id: string;
}

@Component({
  selector: 'app-data-browser',
  standalone: true,
  imports: [CommonModule, LoadingSpinnerComponent],
  template: `
    <div class="data-browser-container">
      <!-- Header -->
      <header class="browser-header">
        <div class="header-content">
          <h1>Data Browser</h1>
          <p>View and manage all field data entries</p>
          <button (click)="goBack()" class="back-btn">← Back to Dashboard</button>
        </div>
      </header>

      <!-- Filters -->
      <section class="filters-section">
        <div class="filters-content">
          <h2>Filter Data</h2>
          <div class="filter-controls">
            <select (change)="onTypeFilterChange($event)" class="filter-select">
              <option value="">All Types</option>
              <option value="host_plant">Host Plants</option>
              <option value="blh_observation">BLH Observations</option>
              <option value="bctv_symptoms">BCTV Symptoms</option>
              <option value="eradication">Eradication Efforts</option>
            </select>

            <select (change)="onDateFilterChange($event)" class="filter-select">
              <option value="">All Time</option>
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="year">This Year</option>
            </select>

            <button (click)="refreshData()" class="refresh-btn" [disabled]="isLoading">
              <span *ngIf="!isLoading">🔄 Refresh</span>
              <app-loading-spinner *ngIf="isLoading" size="small"></app-loading-spinner>
            </button>
          </div>
        </div>
      </section>

      <!-- Data Grid -->
      <main class="data-grid-section">
        <div class="data-grid" *ngIf="!isLoading && filteredEntries.length > 0">
          <div class="data-card" *ngFor="let entry of filteredEntries" (click)="viewEntryDetails(entry)">
            <div class="card-header">
              <div class="entry-type" [class]="entry.type">
                <span class="type-icon">{{getTypeIcon(entry.type)}}</span>
                <span class="type-label">{{getTypeLabel(entry.type)}}</span>
              </div>
              <div class="entry-date">{{entry.created_at | date:'short'}}</div>
            </div>

            <div class="card-body">
              <div class="location-info">
                <span class="location-icon">📍</span>
                <span class="coordinates">{{entry.latitude.toFixed(4)}}, {{entry.longitude.toFixed(4)}}</span>
              </div>

              <div class="entry-summary">
                {{getEntrySummary(entry)}}
              </div>

              <div class="photo-count" *ngIf="entry.photos && entry.photos.length > 0">
                <span class="photo-icon">📷</span>
                <span>{{entry.photos.length}} photo(s)</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Loading State -->
        <div class="loading-state" *ngIf="isLoading">
          <app-loading-spinner size="large" message="Loading data entries..."></app-loading-spinner>
        </div>

        <!-- Empty State -->
        <div class="empty-state" *ngIf="!isLoading && filteredEntries.length === 0">
          <div class="empty-icon">📊</div>
          <h3>No Data Found</h3>
          <p *ngIf="selectedTypeFilter || selectedDateFilter">
            No entries match your current filters. Try adjusting your search criteria.
          </p>
          <p *ngIf="!selectedTypeFilter && !selectedDateFilter">
            No data entries have been recorded yet. Start by logging your first observation.
          </p>
          <button routerLink="/data-entry" class="btn btn-primary">
            Add First Entry
          </button>
        </div>
      </main>
    </div>
  `,
  styles: [`
    .data-browser-container {
      min-height: 100vh;
      background: var(--gray-50);
      display: flex;
      flex-direction: column;
    }

    .browser-header {
      background: white;
      border-bottom: 1px solid var(--gray-200);
      box-shadow: var(--shadow-sm);
      padding: var(--space-6) var(--space-4);
    }

    .header-content h1 {
      font-size: var(--text-2xl);
      font-weight: 700;
      color: var(--gray-900);
      margin: 0 0 var(--space-2) 0;
    }

    .header-content p {
      color: var(--gray-600);
      margin: 0 0 var(--space-4) 0;
    }

    .back-btn {
      background: var(--gray-100);
      color: var(--gray-700);
      border: 1px solid var(--gray-300);
      padding: var(--space-2) var(--space-4);
      border-radius: var(--radius-lg);
      text-decoration: none;
      font-size: var(--text-sm);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
    }

    .back-btn:hover {
      background: var(--gray-200);
      transform: translateY(-1px);
    }

    .filters-section {
      background: white;
      border-bottom: 1px solid var(--gray-200);
      padding: var(--space-6) var(--space-4);
    }

    .filters-content h2 {
      font-size: var(--text-lg);
      font-weight: 600;
      color: var(--gray-900);
      margin: 0 0 var(--space-4) 0;
    }

    .filter-controls {
      display: flex;
      flex-wrap: wrap;
      gap: var(--space-3);
      align-items: center;
    }

    .filter-select {
      padding: var(--space-3) var(--space-4);
      border: 1px solid var(--gray-300);
      border-radius: var(--radius-lg);
      background: white;
      font-size: var(--text-sm);
      color: var(--gray-700);
      min-width: 150px;
      cursor: pointer;
    }

    .filter-select:focus {
      outline: 2px solid var(--primary-500);
      outline-offset: 2px;
      border-color: var(--primary-500);
    }

    .refresh-btn {
      padding: var(--space-3) var(--space-4);
      background: var(--primary-600);
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      font-size: var(--text-sm);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
      min-height: 44px;
      display: flex;
      align-items: center;
      gap: var(--space-2);
    }

    .refresh-btn:hover:not(:disabled) {
      background: var(--primary-700);
      transform: translateY(-1px);
    }

    .refresh-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none;
    }

    .data-grid-section {
      flex: 1;
      padding: var(--space-6) var(--space-4);
    }

    .data-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
      gap: var(--space-6);
      max-width: 1400px;
      margin: 0 auto;
    }

    .data-card {
      background: white;
      border: 1px solid var(--gray-200);
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-sm);
      cursor: pointer;
      transition: all var(--transition-fast);
      overflow: hidden;
    }

    .data-card:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
      border-color: var(--primary-300);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--space-4) var(--space-6);
      background: var(--gray-50);
      border-bottom: 1px solid var(--gray-200);
    }

    .entry-type {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-2) var(--space-3);
      border-radius: var(--radius-lg);
      font-size: var(--text-sm);
      font-weight: 500;
    }

    .entry-type.host_plant {
      background: var(--primary-100);
      color: var(--primary-800);
    }

    .entry-type.blh_observation {
      background: #fef3c7;
      color: var(--accent-yellow);
    }

    .entry-type.bctv_symptoms {
      background: #fee2e2;
      color: var(--accent-red);
    }

    .entry-type.eradication {
      background: #dbeafe;
      color: var(--accent-blue);
    }

    .entry-date {
      font-size: var(--text-xs);
      color: var(--gray-600);
    }

    .card-body {
      padding: var(--space-6);
    }

    .location-info {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      margin-bottom: var(--space-4);
      font-size: var(--text-sm);
      color: var(--gray-600);
    }

    .entry-summary {
      font-size: var(--text-sm);
      color: var(--gray-700);
      line-height: 1.5;
      margin-bottom: var(--space-4);
    }

    .photo-count {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      font-size: var(--text-xs);
      color: var(--gray-500);
    }

    .loading-state,
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: var(--space-16);
      text-align: center;
    }

    .empty-icon {
      font-size: 4rem;
      margin-bottom: var(--space-6);
    }

    .empty-state h3 {
      font-size: var(--text-xl);
      font-weight: 600;
      color: var(--gray-900);
      margin-bottom: var(--space-4);
    }

    .empty-state p {
      color: var(--gray-600);
      margin-bottom: var(--space-6);
      max-width: 400px;
    }

    @media (min-width: 768px) {
      .browser-header,
      .filters-section {
        padding: var(--space-8) var(--space-8);
      }

      .data-grid-section {
        padding: var(--space-8);
      }

      .data-grid {
        grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
      }
    }
  `]
})
export class DataBrowserComponent implements OnInit {
  allEntries: DataEntry[] = [];
  filteredEntries: DataEntry[] = [];
  isLoading = true;
  selectedTypeFilter = '';
  selectedDateFilter = '';

  constructor(
    private supabaseService: SupabaseService,
    private router: Router
  ) {}

  ngOnInit() {
    this.loadData();
  }

  async loadData() {
    this.isLoading = true;
    try {
      const { data: observations, error } = await this.supabaseService.db
        .from('observations')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      this.allEntries = observations || [];
      this.applyFilters();
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      this.isLoading = false;
    }
  }

  onTypeFilterChange(event: Event) {
    const target = event.target as HTMLSelectElement;
    this.selectedTypeFilter = target.value;
    this.applyFilters();
  }

  onDateFilterChange(event: Event) {
    const target = event.target as HTMLSelectElement;
    this.selectedDateFilter = target.value;
    this.applyFilters();
  }

  applyFilters() {
    let filtered = [...this.allEntries];

    // Apply type filter
    if (this.selectedTypeFilter) {
      filtered = filtered.filter(entry => entry.type === this.selectedTypeFilter);
    }

    // Apply date filter
    if (this.selectedDateFilter) {
      const now = new Date();
      const filterDate = new Date();

      switch (this.selectedDateFilter) {
        case 'today':
          filterDate.setHours(0, 0, 0, 0);
          break;
        case 'week':
          filterDate.setDate(now.getDate() - 7);
          break;
        case 'month':
          filterDate.setMonth(now.getMonth() - 1);
          break;
        case 'year':
          filterDate.setFullYear(now.getFullYear() - 1);
          break;
      }

      if (this.selectedDateFilter !== '') {
        filtered = filtered.filter(entry =>
          new Date(entry.created_at) >= filterDate
        );
      }
    }

    this.filteredEntries = filtered;
  }

  refreshData() {
    this.loadData();
  }

  viewEntryDetails(entry: DataEntry) {
    this.router.navigate(['/activity-details', entry.id]);
  }

  getTypeIcon(type: string): string {
    const iconMap: { [key: string]: string } = {
      'host_plant': '🌿',
      'blh_observation': '🦗',
      'bctv_symptoms': '🦠',
      'eradication': '🧹'
    };
    return iconMap[type] || '📝';
  }

  getTypeLabel(type: string): string {
    const labelMap: { [key: string]: string } = {
      'host_plant': 'Host Plant',
      'blh_observation': 'BLH Observation',
      'bctv_symptoms': 'BCTV Symptoms',
      'eradication': 'Eradication'
    };
    return labelMap[type] || type;
  }

  getEntrySummary(entry: DataEntry): string {
    // Generate a summary based on entry type and data
    switch (entry.type) {
      case 'host_plant':
        return `${entry.data?.species || 'Unknown species'} - ${entry.data?.density || 'Unknown density'}`;
      case 'blh_observation':
        return `${entry.data?.adultCount || 0} adults, ${entry.data?.nymphCount || 0} nymphs`;
      case 'bctv_symptoms':
        return `${entry.data?.symptomSeverity || 'Unknown'} severity - ${entry.data?.affectedPlantCount || 0} plants affected`;
      case 'eradication':
        return `${entry.data?.method || 'Unknown method'} - ${entry.data?.areaSize || 0} m² treated`;
      default:
        return 'Field observation data';
    }
  }

  goBack() {
    this.router.navigate(['/dashboard']);
  }
}
