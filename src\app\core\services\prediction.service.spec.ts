import { TestBed } from '@angular/core/testing';
import { PredictionService } from './prediction.service';
import { SupabaseService } from './supabase.service';
import { PredictionRequest, RiskFactorType } from '../models/prediction.model';

describe('PredictionService - Data Integrity Fixes', () => {
  let service: PredictionService;
  let mockSupabaseService: jasmine.SpyObj<SupabaseService>;

  beforeEach(() => {
    const spy = jasmine.createSpyObj('SupabaseService', ['db']);

    TestBed.configureTestingModule({
      providers: [
        PredictionService,
        { provide: SupabaseService, useValue: spy }
      ]
    });

    service = TestBed.inject(PredictionService);
    mockSupabaseService = TestBed.inject(SupabaseService) as jasmine.SpyObj<SupabaseService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should return zero risk when no host plant observations available', async () => {
    // Mock empty observations response
    const mockDbResponse = {
      from: jasmine.createSpy().and.returnValue({
        select: jasmine.createSpy().and.returnValue({
          eq: jasmine.createSpy().and.returnValue({
            gte: jasmine.createSpy().and.returnValue({
              order: jasmine.createSpy().and.returnValue(
                Promise.resolve({ data: [], error: null })
              )
            })
          })
        })
      })
    };

    Object.defineProperty(mockSupabaseService, 'db', {
      value: mockDbResponse,
      writable: true
    });

    const request: PredictionRequest = {
      location: { latitude: 36.7783, longitude: -119.4179 },
      radius: 10,
      timeframe: 7
    };

    const prediction = await service.generatePrediction(request).toPromise();

    expect(prediction).toBeTruthy();

    // Find host plant density factor
    const hostPlantFactor = prediction!.factors.find(f => f.type === RiskFactorType.HOST_PLANT_DENSITY);
    expect(hostPlantFactor).toBeTruthy();
    expect(hostPlantFactor!.value).toBe(0);
    expect(hostPlantFactor!.description).toContain('No observations available');

    // Find BLH population factor
    const blhFactor = prediction!.factors.find(f => f.type === RiskFactorType.BLH_POPULATION);
    expect(blhFactor).toBeTruthy();
    expect(blhFactor!.value).toBe(0);
    expect(blhFactor!.description).toContain('No observations available');

    // Confidence should be low when no data available
    expect(prediction!.confidence).toBeLessThan(0.5);
  });

  it('should not return false observation counts', async () => {
    // Mock empty observations response
    const mockDbResponse = {
      from: jasmine.createSpy().and.returnValue({
        select: jasmine.createSpy().and.returnValue({
          eq: jasmine.createSpy().and.returnValue({
            gte: jasmine.createSpy().and.returnValue({
              order: jasmine.createSpy().and.returnValue(
                Promise.resolve({ data: [], error: null })
              )
            })
          })
        })
      })
    };

    Object.defineProperty(mockSupabaseService, 'db', {
      value: mockDbResponse,
      writable: true
    });

    const request: PredictionRequest = {
      location: { latitude: 36.7783, longitude: -119.4179 },
      radius: 10,
      timeframe: 7
    };

    const prediction = await service.generatePrediction(request).toPromise();

    // Verify no false observation counts in descriptions
    prediction!.factors.forEach(factor => {
      if (factor.description.includes('observations')) {
        // Should either say "No observations" or show actual count > 0
        const hasNoObservations = factor.description.includes('No observations');
        const hasActualCount = /\((\d+) observations\)/.test(factor.description);

        if (hasActualCount) {
          const match = factor.description.match(/\((\d+) observations\)/);
          const count = parseInt(match![1]);
          expect(count).toBeGreaterThan(0);
        }

        expect(hasNoObservations || hasActualCount).toBeTruthy();
      }
    });
  });
});
