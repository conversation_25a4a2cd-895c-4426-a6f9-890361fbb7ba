import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SupabaseService } from '../../../core/services/supabase.service';
import { PhotoService } from '../../../core/services/photo.service';

@Component({
  selector: 'app-photo-upload',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="photo-upload-container">
      <div class="upload-area"
           [class.dragover]="isDragOver"
           (dragover)="onDragOver($event)"
           (dragleave)="onDragLeave($event)"
           (drop)="onDrop($event)"
           (click)="fileInput.click()">

        <input #fileInput
               type="file"
               accept="image/*"
               multiple
               capture="environment"
               (change)="onFileSelected($event)"
               style="display: none;">

        <div class="upload-content">
          <div class="upload-icon">📷</div>
          <p class="upload-text">
            <span class="primary-text">Click to upload photos</span>
            <span class="secondary-text">or drag and drop</span>
          </p>
          <p class="upload-hint">PNG, JPG, JPEG up to 10MB each</p>
        </div>
      </div>

      <div class="photo-preview" *ngIf="photos.length > 0">
        <h4>Selected Photos ({{photos.length}})</h4>
        <div class="photo-grid">
          <div class="photo-item" *ngFor="let photo of photos; let i = index">
            <img [src]="photo.preview" [alt]="photo.file.name" class="photo-thumbnail">
            <div class="photo-overlay">
              <button class="remove-btn" (click)="removePhoto(i)" type="button">×</button>
            </div>
            <div class="photo-info">
              <span class="photo-name">{{photo.file.name}}</span>
              <span class="photo-size">{{formatFileSize(photo.file.size)}}</span>
            </div>
          </div>
        </div>
      </div>

      <div class="upload-progress" *ngIf="isUploading">
        <div class="progress-bar">
          <div class="progress-fill" [style.width.%]="uploadProgress"></div>
        </div>
        <p class="progress-text">Uploading... {{uploadProgress}}%</p>
      </div>

      <div class="error-message" *ngIf="errorMessage">
        <p>{{errorMessage}}</p>
      </div>
    </div>
  `,
  styles: [`
    .photo-upload-container {
      width: 100%;
    }

    .upload-area {
      border: 2px dashed #ccc;
      border-radius: 8px;
      padding: 2rem;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      background-color: #fafafa;
    }

    .upload-area:hover,
    .upload-area.dragover {
      border-color: #007bff;
      background-color: #f0f8ff;
    }

    .upload-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 0.5rem;
    }

    .upload-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }

    .primary-text {
      font-weight: 600;
      color: #333;
    }

    .secondary-text {
      color: #666;
      margin-left: 0.5rem;
    }

    .upload-hint {
      font-size: 0.875rem;
      color: #888;
      margin: 0;
    }

    .photo-preview {
      margin-top: 1.5rem;
    }

    .photo-preview h4 {
      margin-bottom: 1rem;
      color: #333;
    }

    .photo-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      gap: 1rem;
    }

    .photo-item {
      position: relative;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .photo-thumbnail {
      width: 100%;
      height: 120px;
      object-fit: cover;
      display: block;
    }

    .photo-overlay {
      position: absolute;
      top: 0;
      right: 0;
      padding: 0.25rem;
    }

    .remove-btn {
      background: rgba(255, 0, 0, 0.8);
      color: white;
      border: none;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      cursor: pointer;
      font-size: 16px;
      line-height: 1;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .remove-btn:hover {
      background: rgba(255, 0, 0, 1);
    }

    .photo-info {
      padding: 0.5rem;
      background: white;
      display: flex;
      flex-direction: column;
      gap: 0.25rem;
    }

    .photo-name {
      font-size: 0.75rem;
      font-weight: 500;
      color: #333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .photo-size {
      font-size: 0.625rem;
      color: #666;
    }

    .upload-progress {
      margin-top: 1rem;
    }

    .progress-bar {
      width: 100%;
      height: 8px;
      background-color: #e0e0e0;
      border-radius: 4px;
      overflow: hidden;
    }

    .progress-fill {
      height: 100%;
      background-color: #007bff;
      transition: width 0.3s ease;
    }

    .progress-text {
      text-align: center;
      margin-top: 0.5rem;
      font-size: 0.875rem;
      color: #666;
    }

    .error-message {
      margin-top: 1rem;
      padding: 0.75rem;
      background-color: #fee;
      border: 1px solid #fcc;
      border-radius: 4px;
      color: #c33;
    }

    @media (max-width: 768px) {
      .upload-area {
        padding: 1.5rem 1rem;
      }

      .photo-grid {
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 0.75rem;
      }

      .photo-thumbnail {
        height: 100px;
      }
    }
  `]
})
export class PhotoUploadComponent implements OnInit {
  @Input() maxFiles: number = 5;
  @Input() maxFileSize: number = 10 * 1024 * 1024; // 10MB
  @Input() bucket: string = 'observations';
  @Output() photosUploaded = new EventEmitter<string[]>();
  @Output() photosChanged = new EventEmitter<File[]>();

  photos: { file: File; preview: string }[] = [];
  isDragOver = false;
  isUploading = false;
  uploadProgress = 0;
  errorMessage = '';

  constructor(
    private supabaseService: SupabaseService,
    private photoService: PhotoService
  ) {}

  ngOnInit() {}

  onDragOver(event: DragEvent) {
    event.preventDefault();
    this.isDragOver = true;
  }

  onDragLeave(event: DragEvent) {
    event.preventDefault();
    this.isDragOver = false;
  }

  onDrop(event: DragEvent) {
    event.preventDefault();
    this.isDragOver = false;

    const files = Array.from(event.dataTransfer?.files || []);
    this.handleFiles(files);
  }

  onFileSelected(event: Event) {
    const input = event.target as HTMLInputElement;
    const files = Array.from(input.files || []);
    this.handleFiles(files);
  }

  private async handleFiles(files: File[]) {
    this.errorMessage = '';

    // Validate files using PhotoService
    const validFiles: File[] = [];

    for (const file of files) {
      const validation = this.photoService.validateImageFile(file, this.maxFileSize);
      if (!validation.valid) {
        this.errorMessage = validation.error || 'Invalid file';
        continue;
      }
      validFiles.push(file);
    }

    // Check total file count
    if (this.photos.length + validFiles.length > this.maxFiles) {
      this.errorMessage = `Maximum ${this.maxFiles} files allowed`;
      return;
    }

    // Add files to photos array with preview
    for (const file of validFiles) {
      try {
        const preview = await this.photoService.createPreviewUrl(file);
        this.photos.push({ file, preview });
        this.photosChanged.emit(this.photos.map(p => p.file));
      } catch (error) {
        console.error('Error creating preview for file:', file.name, error);
      }
    }
  }

  removePhoto(index: number) {
    this.photos.splice(index, 1);
    this.photosChanged.emit(this.photos.map(p => p.file));
  }

  async uploadPhotos(): Promise<string[]> {
    if (this.photos.length === 0) {
      return [];
    }

    this.isUploading = true;
    this.uploadProgress = 0;
    this.errorMessage = '';

    try {
      const files = this.photos.map(p => p.file);
      const uploadedPaths: string[] = [];

      for (let i = 0; i < files.length; i++) {
        try {
          const filePath = await this.photoService.uploadPhoto(files[i], this.bucket);
          uploadedPaths.push(filePath);

          // Update progress
          this.uploadProgress = Math.round(((i + 1) / files.length) * 100);
        } catch (error) {
          console.error(`Failed to upload file ${files[i].name}:`, error);
          // Continue with other files instead of failing completely
        }
      }

      if (uploadedPaths.length === 0) {
        throw new Error('No photos were uploaded successfully');
      }

      this.photosUploaded.emit(uploadedPaths);

      // Clear photos after successful upload
      this.photos = [];
      this.photosChanged.emit([]);

      return uploadedPaths;
    } catch (error) {
      console.error('Upload error:', error);
      this.errorMessage = 'Failed to upload photos. Please try again.';
      throw error;
    } finally {
      this.isUploading = false;
      this.uploadProgress = 0;
    }
  }

  formatFileSize(bytes: number): string {
    return this.photoService.formatFileSize(bytes);
  }

  // Public method to get current photos
  getPhotos(): File[] {
    return this.photos.map(p => p.file);
  }

  // Public method to clear all photos
  clearPhotos() {
    this.photos = [];
    this.photosChanged.emit([]);
  }
}
