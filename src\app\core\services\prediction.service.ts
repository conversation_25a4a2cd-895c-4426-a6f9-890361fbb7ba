import { Injectable } from '@angular/core';
import { Observable, from, map, catchError, of } from 'rxjs';
import { SupabaseService } from './supabase.service';
import {
  BCTVPrediction,
  RiskLevel,
  RiskFactor,
  RiskFactorType,
  PredictionRequest,
  PredictionRules,
  GeoLocation
} from '../models/prediction.model';
import {
  HostPlantObservation,
  BLHObservation,
  PlantDensity,
  BLHDensity
} from '../models/observation.model';

@Injectable({
  providedIn: 'root'
})
export class PredictionService {
  private readonly predictionRules: PredictionRules = {
    hostPlantDensityThresholds: {
      low: 10,    // plants per m²
      medium: 50,
      high: 100
    },
    blhDensityThresholds: {
      low: 5,     // BLH per plant
      medium: 15,
      high: 30
    },
    weatherFactors: {
      optimalTemperatureRange: [20, 30], // Celsius
      optimalHumidityRange: [40, 70],    // percentage
      windSpeedThreshold: 15             // km/h
    },
    seasonalFactors: {
      highRiskMonths: [4, 5, 6, 7, 8, 9], // April to September
      moderateRiskMonths: [3, 10]         // March, October
    }
  };

  constructor(private supabaseService: SupabaseService) {}

  generatePrediction(request: PredictionRequest): Observable<BCTVPrediction> {
    return from(this.calculateRiskAssessment(request)).pipe(
      map(prediction => prediction),
      catchError(error => {
        console.error('Prediction generation error:', error);
        throw error;
      })
    );
  }

  private async calculateRiskAssessment(request: PredictionRequest): Promise<BCTVPrediction> {
    const factors: RiskFactor[] = [];
    let totalScore = 0;
    let totalWeight = 0;

    // 1. Analyze host plant density in the area
    const hostPlantFactor = await this.analyzeHostPlantDensity(request.location, request.radius);
    factors.push(hostPlantFactor);
    totalScore += hostPlantFactor.value * hostPlantFactor.weight;
    totalWeight += hostPlantFactor.weight;

    // 2. Analyze BLH population
    const blhFactor = await this.analyzeBLHPopulation(request.location, request.radius);
    factors.push(blhFactor);
    totalScore += blhFactor.value * blhFactor.weight;
    totalWeight += blhFactor.weight;

    // 3. Analyze weather conditions
    const weatherFactor = this.analyzeWeatherConditions();
    factors.push(weatherFactor);
    totalScore += weatherFactor.value * weatherFactor.weight;
    totalWeight += weatherFactor.weight;

    // 4. Analyze seasonal factors
    const seasonalFactor = this.analyzeSeasonalFactors();
    factors.push(seasonalFactor);
    totalScore += seasonalFactor.value * seasonalFactor.weight;
    totalWeight += seasonalFactor.weight;

    // 5. Analyze historical outbreaks
    const historicalFactor = await this.analyzeHistoricalOutbreaks(request.location, request.radius);
    factors.push(historicalFactor);
    totalScore += historicalFactor.value * historicalFactor.weight;
    totalWeight += historicalFactor.weight;

    // Calculate final risk score
    const riskScore = totalWeight > 0 ? (totalScore / totalWeight) * 100 : 0;
    const riskLevel = this.calculateRiskLevel(riskScore);
    const recommendations = this.generateRecommendations(riskLevel, factors);

    const prediction: BCTVPrediction = {
      id: this.generateId(),
      location: request.location,
      riskLevel,
      riskScore: Math.round(riskScore),
      factors,
      recommendations,
      validFrom: new Date(),
      validUntil: new Date(Date.now() + request.timeframe * 24 * 60 * 60 * 1000),
      confidence: this.calculateConfidence(factors),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    return prediction;
  }

  private async analyzeHostPlantDensity(location: GeoLocation, radius: number): Promise<RiskFactor> {
    try {
      // Query recent host plant observations within radius
      const { data: observations, error } = await this.supabaseService.db
        .from('observations')
        .select('*')
        .eq('type', 'host_plant')
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // Last 30 days
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Filter observations within radius (simplified calculation)
      const nearbyObservations = observations?.filter(obs => {
        const distance = this.calculateDistance(
          location.latitude, location.longitude,
          obs.latitude, obs.longitude
        );
        return distance <= radius;
      }) || [];

      let averageDensity = 0;
      if (nearbyObservations.length > 0) {
        const densityValues = nearbyObservations.map(obs => this.getDensityValue(obs.host_plant_data?.density));
        averageDensity = densityValues.reduce((sum, val) => sum + val, 0) / densityValues.length;
      } else {
        // No observations found - return zero risk with clear indication
        return {
          type: RiskFactorType.HOST_PLANT_DENSITY,
          value: 0,
          weight: 0.3,
          description: 'Host plant density: No observations available in this area'
        };
      }

      const value = Math.min(averageDensity / this.predictionRules.hostPlantDensityThresholds.high, 1);

      return {
        type: RiskFactorType.HOST_PLANT_DENSITY,
        value,
        weight: 0.3,
        description: `Host plant density: ${averageDensity.toFixed(1)} plants/m² (${nearbyObservations.length} observations)`
      };
    } catch (error) {
      console.error('Error analyzing host plant density:', error);
      return {
        type: RiskFactorType.HOST_PLANT_DENSITY,
        value: 0,
        weight: 0.3,
        description: 'Host plant density: Unable to analyze data'
      };
    }
  }

  private async analyzeBLHPopulation(location: GeoLocation, radius: number): Promise<RiskFactor> {
    try {
      // Query recent BLH observations within radius
      const { data: observations, error } = await this.supabaseService.db
        .from('observations')
        .select('*')
        .eq('type', 'blh_observation')
        .gte('created_at', new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString()) // Last 14 days
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Filter observations within radius
      const nearbyObservations = observations?.filter(obs => {
        const distance = this.calculateDistance(
          location.latitude, location.longitude,
          obs.latitude, obs.longitude
        );
        return distance <= radius;
      }) || [];

      let averageDensity = 0;
      if (nearbyObservations.length > 0) {
        const densityValues = nearbyObservations.map(obs => this.getBLHDensityValue(obs.blh_data?.density));
        averageDensity = densityValues.reduce((sum, val) => sum + val, 0) / densityValues.length;
      } else {
        // No observations found - return zero risk with clear indication
        return {
          type: RiskFactorType.BLH_POPULATION,
          value: 0,
          weight: 0.35,
          description: 'BLH population: No observations available in this area'
        };
      }

      const value = Math.min(averageDensity / this.predictionRules.blhDensityThresholds.high, 1);

      return {
        type: RiskFactorType.BLH_POPULATION,
        value,
        weight: 0.35,
        description: `BLH population: ${averageDensity.toFixed(1)} per plant (${nearbyObservations.length} observations)`
      };
    } catch (error) {
      console.error('Error analyzing BLH population:', error);
      return {
        type: RiskFactorType.BLH_POPULATION,
        value: 0,
        weight: 0.35,
        description: 'BLH population: Unable to analyze data'
      };
    }
  }

  private analyzeWeatherConditions(): RiskFactor {
    // CRITICAL: No simulated weather data - real weather API integration required
    // Returning zero risk with clear indication that weather data is unavailable

    return {
      type: RiskFactorType.WEATHER_CONDITIONS,
      value: 0,
      weight: 0.2,
      description: 'Weather conditions: Real-time weather data unavailable - API integration required'
    };
  }

  private analyzeSeasonalFactors(): RiskFactor {
    const currentMonth = new Date().getMonth() + 1;
    let seasonalValue = 0;

    if (this.predictionRules.seasonalFactors.highRiskMonths.includes(currentMonth)) {
      seasonalValue = 0.8;
    } else if (this.predictionRules.seasonalFactors.moderateRiskMonths.includes(currentMonth)) {
      seasonalValue = 0.5;
    } else {
      seasonalValue = 0.2;
    }

    return {
      type: RiskFactorType.SEASONAL_FACTORS,
      value: seasonalValue,
      weight: 0.1,
      description: `Seasonal risk: ${this.getMonthName(currentMonth)}`
    };
  }

  private async analyzeHistoricalOutbreaks(location: GeoLocation, radius: number): Promise<RiskFactor> {
    try {
      // Query historical BCTV symptom observations
      const { data: observations, error } = await this.supabaseService.db
        .from('observations')
        .select('*')
        .eq('type', 'bctv_symptoms')
        .gte('created_at', new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString()) // Last year
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Filter observations within radius
      const nearbyOutbreaks = observations?.filter(obs => {
        const distance = this.calculateDistance(
          location.latitude, location.longitude,
          obs.latitude, obs.longitude
        );
        return distance <= radius;
      }) || [];

      const value = Math.min(nearbyOutbreaks.length / 10, 1); // Normalize to 0-1 scale

      return {
        type: RiskFactorType.HISTORICAL_OUTBREAKS,
        value,
        weight: 0.05,
        description: `Historical outbreaks: ${nearbyOutbreaks.length} in past year`
      };
    } catch (error) {
      console.error('Error analyzing historical outbreaks:', error);
      return {
        type: RiskFactorType.HISTORICAL_OUTBREAKS,
        value: 0,
        weight: 0.05,
        description: 'Historical outbreaks: Data unavailable'
      };
    }
  }

  private calculateRiskLevel(riskScore: number): RiskLevel {
    if (riskScore >= 80) return RiskLevel.VERY_HIGH;
    if (riskScore >= 60) return RiskLevel.HIGH;
    if (riskScore >= 40) return RiskLevel.MODERATE;
    if (riskScore >= 20) return RiskLevel.LOW;
    return RiskLevel.VERY_LOW;
  }

  private generateRecommendations(riskLevel: RiskLevel, factors: RiskFactor[]): string[] {
    const recommendations: string[] = [];

    switch (riskLevel) {
      case RiskLevel.VERY_HIGH:
        recommendations.push('Immediate action required: Implement comprehensive weed control measures');
        recommendations.push('Increase monitoring frequency to daily inspections');
        recommendations.push('Consider emergency herbicide applications');
        recommendations.push('Coordinate with neighboring properties for area-wide management');
        break;

      case RiskLevel.HIGH:
        recommendations.push('Urgent action needed: Begin intensive weed management');
        recommendations.push('Monitor BLH populations closely');
        recommendations.push('Implement targeted herbicide treatments');
        recommendations.push('Increase surveillance in surrounding areas');
        break;

      case RiskLevel.MODERATE:
        recommendations.push('Preventive measures recommended');
        recommendations.push('Regular monitoring of host plants and BLH populations');
        recommendations.push('Consider selective weed control in high-density areas');
        recommendations.push('Maintain good agricultural practices');
        break;

      case RiskLevel.LOW:
        recommendations.push('Continue routine monitoring');
        recommendations.push('Maintain current management practices');
        recommendations.push('Monitor for changes in host plant density');
        break;

      case RiskLevel.VERY_LOW:
        recommendations.push('Standard monitoring protocols sufficient');
        recommendations.push('Focus on prevention and early detection');
        break;
    }

    // Add specific recommendations based on risk factors
    const hostPlantFactor = factors.find(f => f.type === RiskFactorType.HOST_PLANT_DENSITY);
    if (hostPlantFactor && hostPlantFactor.value > 0.7) {
      recommendations.push('High host plant density detected - prioritize weed control');
    }

    const blhFactor = factors.find(f => f.type === RiskFactorType.BLH_POPULATION);
    if (blhFactor && blhFactor.value > 0.6) {
      recommendations.push('Elevated BLH populations - consider insecticide treatments');
    }

    return recommendations;
  }

  private calculateConfidence(factors: RiskFactor[]): number {
    // Confidence based on data availability and recency
    let confidence = 0.1; // Lower base confidence when no data available

    const hostPlantFactor = factors.find(f => f.type === RiskFactorType.HOST_PLANT_DENSITY);
    const blhFactor = factors.find(f => f.type === RiskFactorType.BLH_POPULATION);
    const weatherFactor = factors.find(f => f.type === RiskFactorType.WEATHER_CONDITIONS);

    // Check for actual data availability (not just "unavailable" but also "No observations")
    if (hostPlantFactor && !hostPlantFactor.description.includes('unavailable') && !hostPlantFactor.description.includes('No observations')) {
      confidence += 0.3;
    }

    if (blhFactor && !blhFactor.description.includes('unavailable') && !blhFactor.description.includes('No observations')) {
      confidence += 0.3;
    }

    // Weather data confidence - only add if real weather data is available
    if (weatherFactor && !weatherFactor.description.includes('unavailable') && !weatherFactor.description.includes('API integration required')) {
      confidence += 0.2;
    }

    // Seasonal confidence (always available)
    confidence += 0.1;

    return Math.min(confidence, 1);
  }

  private getDensityValue(density: PlantDensity | string): number {
    switch (density) {
      case PlantDensity.LOW: return 5;
      case PlantDensity.MEDIUM: return 30;
      case PlantDensity.HIGH: return 75;
      case PlantDensity.VERY_HIGH: return 150;
      default: return 0;
    }
  }

  private getBLHDensityValue(density: BLHDensity | string): number {
    switch (density) {
      case BLHDensity.NONE: return 0;
      case BLHDensity.LOW: return 3;
      case BLHDensity.MEDIUM: return 10;
      case BLHDensity.HIGH: return 23;
      case BLHDensity.VERY_HIGH: return 40;
      default: return 0;
    }
  }

  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);

    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  private getMonthName(month: number): string {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month - 1] || 'Unknown';
  }

  private generateId(): string {
    return 'pred_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  // Public methods for getting predictions
  getPredictionHistory(location: GeoLocation, limit: number = 10): Observable<BCTVPrediction[]> {
    // In a real implementation, this would query stored predictions
    return of([]);
  }

  savePrediction(prediction: BCTVPrediction): Observable<BCTVPrediction> {
    return from(
      this.supabaseService.db
        .from('predictions')
        .insert({
          id: prediction.id,
          location: prediction.location,
          risk_level: prediction.riskLevel,
          risk_score: prediction.riskScore,
          factors: prediction.factors,
          recommendations: prediction.recommendations,
          valid_from: prediction.validFrom.toISOString(),
          valid_until: prediction.validUntil.toISOString(),
          confidence: prediction.confidence,
          created_at: prediction.createdAt.toISOString(),
          updated_at: prediction.updatedAt.toISOString()
        })
    ).pipe(
      map(({ error }) => {
        if (error) throw error;
        return prediction;
      }),
      catchError(error => {
        console.error('Error saving prediction:', error);
        throw error;
      })
    );
  }
}
