# BCTV Management System

A comprehensive web application for Beet Curly Top Virus (BCTV) hotspot prediction and eradication support in California agriculture.

## Overview

This Angular-based application provides field workers, researchers, and administrators with tools to:
- Record field observations of host plants and BLH populations
- Document BCTV symptoms and eradication efforts
- Generate AI-powered risk predictions
- Visualize data on interactive maps
- Track and analyze outbreak patterns

## Technology Stack

- **Frontend**: Angular 19 with SSR
- **Backend**: Supabase (PostgreSQL with PostGIS)
- **Mapping**: MapLibre GL JS
- **Storage**: Supabase Storage for photos
- **Authentication**: Supabase Auth

## Features

### ✅ Implemented
- **User authentication and role-based access** with Supabase Auth
- **Interactive map dashboard** with accurate California state borders
- **Complete data entry system** with all observation types:
  - Host plant observation forms (10 key BCTV host weeds)
  - BLH (Beet Leafhopper) population tracking
  - BCTV symptoms documentation with severity assessment
  - Eradication effort tracking with effectiveness monitoring
- **Advanced map features**:
  - Accurate California state borders with detailed coordinates
  - Major California cities with interactive markers
  - Terrain features (rivers, topography)
  - Working heat map toggle functionality
  - Risk area overlay with color-coded BCTV prediction zones
  - Interactive legend positioned over map
- **Comprehensive data management**:
  - Data browser with filtering and search capabilities
  - Clickable recent activity items with detailed views
  - Activity details pages with full metadata and photos
  - Fixed data entry navigation showing all available forms
- **Enhanced predictions**:
  - Natural language explanations for all predictions
  - Risk level explanations in field specialist-friendly language
  - Factor-specific explanations for each risk component
  - Confidence levels and validity periods
- **Mobile-first responsive design**:
  - Touch-optimized interface with 44px minimum touch targets
  - Hamburger navigation menu for mobile devices
  - Efficient screen real estate usage
  - Professional modern 2025 web application aesthetic
- **Professional sidebar sections**:
  - Color-coded Risk Summary with visual progress bars
  - Timeline-style Recent Activity feed with rich metadata
  - Interactive elements with smooth hover effects
- **Photo upload with geolocation** and gallery views
- **Real-time data updates** and auto-refresh functionality

### ✅ Recently Completed (December 2024)
- **Mobile-first redesign** with modern CSS architecture
- **Map functionality fixes** including heat map and risk overlays
- **Visual design modernization** with contemporary icons and typography
- **Enhanced user experience** with natural language prediction explanations
- **Professional styling** for Risk Summary and Recent Activity sections
- **Comprehensive documentation** update

## Quick Start

### Prerequisites
- Node.js 18+
- Angular CLI 19+
- Supabase account

### Installation
```bash
# Clone the repository
git clone <repository-url>
cd my-angular-app

# Install dependencies
npm install

# Set up environment variables (see Environment Setup below)
# Start development server
ng serve
```

### Environment Setup
Create a Supabase project and update the environment files:

```typescript
// src/environments/environment.ts
export const environment = {
  production: false,
  supabase: {
    url: 'YOUR_SUPABASE_URL',
    anonKey: 'YOUR_SUPABASE_ANON_KEY'
  },
  maplibre: {
    style: 'https://demotiles.maplibre.org/style.json'
  }
};
```

## ✅ Recent Updates

### Complete Mobile-First Redesign - COMPLETED ✅
**Status**: ✅ COMPLETED - Application fully optimized for field specialists
**Completion Date**: December 2024

**Major Improvements Implemented**:
- ✅ **Mobile-First Responsive Design**: Complete restructure for smartphone field use
- ✅ **Modern Visual Design**: 2025 aesthetic with professional agricultural theming
- ✅ **Enhanced Map Functionality**: Fixed heat map toggle, added California borders, cities, and terrain
- ✅ **Risk Area Overlay**: Color-coded BCTV prediction zones with toggle functionality
- ✅ **Professional Sidebar Sections**: Redesigned Risk Summary and Recent Activity with rich styling
- ✅ **Natural Language Predictions**: Field specialist-friendly explanations for all predictions
- ✅ **Comprehensive Data Management**: Data browser, activity details, and improved navigation
- ✅ **Touch-Optimized Interface**: 44px minimum touch targets and gesture-friendly interactions

### California Border & Map Legend - FIXED ✅
**Status**: ✅ FIXED - Map now shows accurate California state shape
**Resolution Date**: December 2024

**Issues Fixed**:
- ✅ Replaced rectangular border with accurate California state coordinates (80+ points)
- ✅ Removed random lines and improved river positioning
- ✅ Positioned map legend over the map (bottom-left) instead of screen bottom
- ✅ Added proper z-index and styling for legend visibility
- ✅ Implemented California state fill with subtle blue overlay

### Risk Summary & Recent Activity - COMPLETELY REDESIGNED ✅
**Status**: ✅ COMPLETED - Professional, fully-featured sidebar sections
**Completion Date**: December 2024

**New Features**:
- ✅ **Risk Summary**: Color-coded badges, progress bars, risk icons, and "View Full Report" button
- ✅ **Recent Activity**: Timeline-style feed with activity icons, descriptions, and clickable items
- ✅ **Rich Metadata**: Location coordinates, relative timestamps, and detailed descriptions
- ✅ **Interactive Elements**: Hover effects, smooth transitions, and professional styling
- ✅ **Loading States**: Professional spinners and error handling

### Previous Issues - ALL RESOLVED ✅
- ✅ **Loading Screen Issue** (January 2025): Application loads correctly
- ✅ **Phantom Data Issue** (January 2025): Real data integration completed
- ✅ **Map Marker Display** (January 2025): Markers display correctly with proper styling
- ✅ **Authentication Flow** (January 2025): Secure login and registration working
- ✅ **Data Entry Navigation** (December 2024): All forms accessible from navigation hub

**Technical Architecture**:
- Modern CSS with custom properties and design tokens
- Mobile-first responsive breakpoints (768px, 1024px, 1280px)
- Component-based architecture with standalone Angular components
- Real-time data integration with Supabase
- Professional error handling and loading states
- Comprehensive TypeScript type safety

**Documentation Updated**:
- `IMPLEMENTATION_DOCUMENTATION.md` - Complete technical documentation
- `README.md` - Updated project overview and features
- Inline code documentation and comments
- Component documentation with usage examples

## Application Structure

```
src/app/
├── core/
│   ├── models/          # Data models and types
│   ├── services/        # Core services (auth, data, etc.)
│   └── guards/          # Route guards
├── features/
│   ├── auth/           # Authentication components
│   ├── dashboard/      # Main dashboard with map
│   ├── data-entry/     # Data collection forms
│   └── predictions/    # Risk prediction views
├── shared/
│   └── components/     # Reusable components
└── environments/       # Environment configurations
```

## Key Components

### Data Models
- **Host Plants**: 10 key BCTV host weeds with density tracking
- **BLH Observations**: Population counts and behavior patterns
- **BCTV Symptoms**: Disease severity and symptom types
- **Eradication Efforts**: Control methods and effectiveness

### Prediction Engine
The application includes a rule-based prediction system that analyzes:
- Host plant density and distribution
- Beet leafhopper populations
- Weather conditions
- Seasonal factors
- Historical outbreak data

### Mobile Features
- GPS integration for accurate location data
- Photo capture and upload
- Offline-capable forms (planned)
- Touch-optimized interface

## User Roles

- **Field Worker**: Data collection and basic reporting
- **Researcher**: Advanced analytics and data export
- **Administrator**: User management and system configuration

## Development

### Running Tests
```bash
# Unit tests
ng test

# E2E tests
ng e2e
```

### Building for Production
```bash
ng build --configuration production
```

### Database Setup
See `DEVELOPMENT_PROGRESS.md` for detailed database schema and setup instructions.

## Contributing

1. Review the `DEVELOPMENT_PROGRESS.md` file for current status
2. Check the roadmap for planned features
3. Follow the established code structure and patterns
4. Ensure mobile responsiveness for all new features
5. Add appropriate tests for new functionality

## License

This project is developed for California agricultural research and management purposes.

## Support

For technical issues or feature requests, please refer to the development roadmap in `DEVELOPMENT_PROGRESS.md`.

---

**Note**: This application is specifically designed for California agricultural use and includes validation for California geographic bounds. The system focuses on the 10 key BCTV host weeds identified as critical for monitoring in California agriculture.
