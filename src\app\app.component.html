<!-- BCTV Application Shell -->
<div class="app-container">
  <!-- Loading Spinner Overlay -->
  <div *ngIf="isLoading" class="loading-overlay">
    <div class="loading-content">
      <div class="spinner"></div>
      <p class="loading-message">{{ loadingMessage }}</p>
    </div>
  </div>

  <!-- Main Application Content -->

  <router-outlet></router-outlet>
</div>

<!-- BCTV Application Styles -->
<style>
  .app-container {
    position: relative;
    width: 100%;
    min-height: 100vh;
    overflow: auto;
  }

  .loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
  }

  .loading-content {
    text-align: center;
    color: #2c5530;
  }

  .loading-message {
    margin-top: 1rem;
    font-size: 1.1rem;
    font-weight: 500;
  }

  .spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #e8f5e8;
    border-top: 4px solid #2c5530;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
</style>